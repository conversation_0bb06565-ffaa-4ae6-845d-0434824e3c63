/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// FILE: GameInfoWindow.h ///////////////////////////////////////////////////////////////////////////
// Created:    Chris Huybregts, Feb 2002
// Desc:       Game Info Window Header
///////////////////////////////////////////////////////////////////////////////////////////////////

#pragma once

#ifndef __GAMEINFOWINDOW_H_
#define __GAMEINFOWINDOW_H_

// INCLUDES ///////////////////////////////////////////////////////////////////////////////////////
#include "GameClient/GameWindow.h"
#include "GameNetwork/LANGameInfo.h"

// Function Stubs for GameInfoWindow
extern void CreateLANGameInfoWindow( GameWindow *sizeAndPosWin );
extern void DestroyGameInfoWindow(void);
extern void RefreshGameInfoWindow(GameInfo *gameInfo, UnicodeString gameName);
extern void HideGameInfoWindow(Bool hide);

#endif // __GAMEINFOWINDOW_H_

