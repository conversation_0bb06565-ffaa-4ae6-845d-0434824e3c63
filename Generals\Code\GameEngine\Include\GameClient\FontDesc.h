/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// FILE: FontDesc.h ///////////////////////////////////////////////////////////////////////////////
// Simple structure used to hold font descriptions.
// Author: Mark Wilczynski, October 2002

#pragma once
#ifndef _FONTDESC_H_
#define _FONTDESC_H_

#include "Common/GameType.h"

struct FontDesc
{
	FontDesc(void);
	AsciiString name;	///<name of font
	Int	size;			///<point size
	Bool bold;			///<is bold?
};

#endif // _FONTDESC_H_
