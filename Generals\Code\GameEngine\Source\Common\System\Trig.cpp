/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// Trig.cpp
// fast trig functions
// Author: Michael S. Booth, March 1994
// Converted to Generals by Matthew D. Campbell, February 2002

#include "PreRTS.h"

#include <math.h>
#include <limits.h>

#include "Lib/BaseType.h"
#include "Lib/Trig.h"

#define TWOPI			6.28318530718f
#define DEG2RAD 	0.0174532925199f
#define TRIG_RES 4096

// the following are for fixed point ints with 12 fractional bits
#define INT_ONE								4096
#define INT_TWOPI							25736
#define INT_THREEPIOVERTWO 		19302
#define INT_PI								12868
#define INT_HALFPI 						6434
#define intCos(x) intSin( INT_HALFPI - (x) )

// sinLookup and arcCosLookup were statically initialized on a mcampbell2k, a 1GHz PIII running Win2K
// on Feb 12, 2002.  To regenerate, define REGENERATE_TRIG_TABLES, run the program, and copy the
// resulting trig.txt file here.

static Int sinLookup[TRIG_RES] = {
	0x00000000, 0x00000006, 0x0000000C, 0x00000012, 0x00000019, 0x0000001F, 0x00000025, 0x0000002B,
	0x00000032, 0x00000038, 0x0000003E, 0x00000045, 0x0000004B, 0x00000051, 0x00000057, 0x0000005E,
	0x00000064, 0x0000006A, 0x00000071, 0x00000077, 0x0000007D, 0x00000083, 0x0000008A, 0x00000090,
	0x00000096, 0x0000009D, 0x000000A3, 0x000000A9, 0x000000AF, 0x000000B6, 0x000000BC, 0x000000C2,
	0x000000C8, 0x000000CF, 0x000000D5, 0x000000DB, 0x000000E2, 0x000000E8, 0x000000EE, 0x000000F4,
	0x000000FB, 0x00000101, 0x00000107, 0x0000010D, 0x00000114, 0x0000011A, 0x00000120, 0x00000127,
	0x0000012D, 0x00000133, 0x00000139, 0x00000140, 0x00000146, 0x0000014C, 0x00000152, 0x00000159,
	0x0000015F, 0x00000165, 0x0000016B, 0x00000172, 0x00000178, 0x0000017E, 0x00000184, 0x0000018B,
	0x00000191, 0x00000197, 0x0000019D, 0x000001A4, 0x000001AA, 0x000001B0, 0x000001B6, 0x000001BD,
	0x000001C3, 0x000001C9, 0x000001CF, 0x000001D6, 0x000001DC, 0x000001E2, 0x000001E8, 0x000001EF,
	0x000001F5, 0x000001FB, 0x00000201, 0x00000208, 0x0000020E, 0x00000214, 0x0000021A, 0x00000221,
	0x00000227, 0x0000022D, 0x00000233, 0x00000239, 0x00000240, 0x00000246, 0x0000024C, 0x00000252,
	0x00000259, 0x0000025F, 0x00000265, 0x0000026B, 0x00000271, 0x00000278, 0x0000027E, 0x00000284,
	0x0000028A, 0x00000290, 0x00000297, 0x0000029D, 0x000002A3, 0x000002A9, 0x000002AF, 0x000002B6,
	0x000002BC, 0x000002C2, 0x000002C8, 0x000002CE, 0x000002D5, 0x000002DB, 0x000002E1, 0x000002E7,
	0x000002ED, 0x000002F3, 0x000002FA, 0x00000300, 0x00000306, 0x0000030C, 0x00000312, 0x00000318,
	0x0000031F, 0x00000325, 0x0000032B, 0x00000331, 0x00000337, 0x0000033D, 0x00000344, 0x0000034A,
	0x00000350, 0x00000356, 0x0000035C, 0x00000362, 0x00000368, 0x0000036F, 0x00000375, 0x0000037B,
	0x00000381, 0x00000387, 0x0000038D, 0x00000393, 0x00000399, 0x000003A0, 0x000003A6, 0x000003AC,
	0x000003B2, 0x000003B8, 0x000003BE, 0x000003C4, 0x000003CA, 0x000003D0, 0x000003D7, 0x000003DD,
	0x000003E3, 0x000003E9, 0x000003EF, 0x000003F5, 0x000003FB, 0x00000401, 0x00000407, 0x0000040D,
	0x00000413, 0x0000041A, 0x00000420, 0x00000426, 0x0000042C, 0x00000432, 0x00000438, 0x0000043E,
	0x00000444, 0x0000044A, 0x00000450, 0x00000456, 0x0000045C, 0x00000462, 0x00000468, 0x0000046E,
	0x00000474, 0x0000047A, 0x00000480, 0x00000486, 0x0000048C, 0x00000492, 0x00000498, 0x0000049E,
	0x000004A5, 0x000004AB, 0x000004B1, 0x000004B7, 0x000004BD, 0x000004C3, 0x000004C9, 0x000004CF,
	0x000004D5, 0x000004DB, 0x000004E0, 0x000004E6, 0x000004EC, 0x000004F2, 0x000004F8, 0x000004FE,
	0x00000504, 0x0000050A, 0x00000510, 0x00000516, 0x0000051C, 0x00000522, 0x00000528, 0x0000052E,
	0x00000534, 0x0000053A, 0x00000540, 0x00000546, 0x0000054C, 0x00000552, 0x00000558, 0x0000055D,
	0x00000563, 0x00000569, 0x0000056F, 0x00000575, 0x0000057B, 0x00000581, 0x00000587, 0x0000058D,
	0x00000593, 0x00000599, 0x0000059E, 0x000005A4, 0x000005AA, 0x000005B0, 0x000005B6, 0x000005BC,
	0x000005C2, 0x000005C7, 0x000005CD, 0x000005D3, 0x000005D9, 0x000005DF, 0x000005E5, 0x000005EB,
	0x000005F0, 0x000005F6, 0x000005FC, 0x00000602, 0x00000608, 0x0000060E, 0x00000613, 0x00000619,
	0x0000061F, 0x00000625, 0x0000062B, 0x00000630, 0x00000636, 0x0000063C, 0x00000642, 0x00000648,
	0x0000064D, 0x00000653, 0x00000659, 0x0000065F, 0x00000664, 0x0000066A, 0x00000670, 0x00000676,
	0x0000067B, 0x00000681, 0x00000687, 0x0000068D, 0x00000692, 0x00000698, 0x0000069E, 0x000006A3,
	0x000006A9, 0x000006AF, 0x000006B5, 0x000006BA, 0x000006C0, 0x000006C6, 0x000006CB, 0x000006D1,
	0x000006D7, 0x000006DC, 0x000006E2, 0x000006E8, 0x000006ED, 0x000006F3, 0x000006F9, 0x000006FE,
	0x00000704, 0x0000070A, 0x0000070F, 0x00000715, 0x0000071B, 0x00000720, 0x00000726, 0x0000072B,
	0x00000731, 0x00000737, 0x0000073C, 0x00000742, 0x00000748, 0x0000074D, 0x00000753, 0x00000758,
	0x0000075E, 0x00000763, 0x00000769, 0x0000076F, 0x00000774, 0x0000077A, 0x0000077F, 0x00000785,
	0x0000078A, 0x00000790, 0x00000795, 0x0000079B, 0x000007A0, 0x000007A6, 0x000007AC, 0x000007B1,
	0x000007B7, 0x000007BC, 0x000007C2, 0x000007C7, 0x000007CD, 0x000007D2, 0x000007D7, 0x000007DD,
	0x000007E2, 0x000007E8, 0x000007ED, 0x000007F3, 0x000007F8, 0x000007FE, 0x00000803, 0x00000809,
	0x0000080E, 0x00000813, 0x00000819, 0x0000081E, 0x00000824, 0x00000829, 0x0000082E, 0x00000834,
	0x00000839, 0x0000083F, 0x00000844, 0x00000849, 0x0000084F, 0x00000854, 0x0000085A, 0x0000085F,
	0x00000864, 0x0000086A, 0x0000086F, 0x00000874, 0x0000087A, 0x0000087F, 0x00000884, 0x0000088A,
	0x0000088F, 0x00000894, 0x00000899, 0x0000089F, 0x000008A4, 0x000008A9, 0x000008AF, 0x000008B4,
	0x000008B9, 0x000008BE, 0x000008C4, 0x000008C9, 0x000008CE, 0x000008D3, 0x000008D9, 0x000008DE,
	0x000008E3, 0x000008E8, 0x000008EE, 0x000008F3, 0x000008F8, 0x000008FD, 0x00000902, 0x00000908,
	0x0000090D, 0x00000912, 0x00000917, 0x0000091C, 0x00000921, 0x00000927, 0x0000092C, 0x00000931,
	0x00000936, 0x0000093B, 0x00000940, 0x00000945, 0x0000094B, 0x00000950, 0x00000955, 0x0000095A,
	0x0000095F, 0x00000964, 0x00000969, 0x0000096E, 0x00000973, 0x00000978, 0x0000097D, 0x00000982,
	0x00000987, 0x0000098D, 0x00000992, 0x00000997, 0x0000099C, 0x000009A1, 0x000009A6, 0x000009AB,
	0x000009B0, 0x000009B5, 0x000009BA, 0x000009BF, 0x000009C4, 0x000009C9, 0x000009CE, 0x000009D3,
	0x000009D7, 0x000009DC, 0x000009E1, 0x000009E6, 0x000009EB, 0x000009F0, 0x000009F5, 0x000009FA,
	0x000009FF, 0x00000A04, 0x00000A09, 0x00000A0E, 0x00000A12, 0x00000A17, 0x00000A1C, 0x00000A21,
	0x00000A26, 0x00000A2B, 0x00000A30, 0x00000A35, 0x00000A39, 0x00000A3E, 0x00000A43, 0x00000A48,
	0x00000A4D, 0x00000A51, 0x00000A56, 0x00000A5B, 0x00000A60, 0x00000A65, 0x00000A69, 0x00000A6E,
	0x00000A73, 0x00000A78, 0x00000A7C, 0x00000A81, 0x00000A86, 0x00000A8B, 0x00000A8F, 0x00000A94,
	0x00000A99, 0x00000A9D, 0x00000AA2, 0x00000AA7, 0x00000AAC, 0x00000AB0, 0x00000AB5, 0x00000ABA,
	0x00000ABE, 0x00000AC3, 0x00000AC8, 0x00000ACC, 0x00000AD1, 0x00000AD5, 0x00000ADA, 0x00000ADF,
	0x00000AE3, 0x00000AE8, 0x00000AEC, 0x00000AF1, 0x00000AF6, 0x00000AFA, 0x00000AFF, 0x00000B03,
	0x00000B08, 0x00000B0C, 0x00000B11, 0x00000B15, 0x00000B1A, 0x00000B1F, 0x00000B23, 0x00000B28,
	0x00000B2C, 0x00000B31, 0x00000B35, 0x00000B3A, 0x00000B3E, 0x00000B42, 0x00000B47, 0x00000B4B,
	0x00000B50, 0x00000B54, 0x00000B59, 0x00000B5D, 0x00000B62, 0x00000B66, 0x00000B6A, 0x00000B6F,
	0x00000B73, 0x00000B78, 0x00000B7C, 0x00000B80, 0x00000B85, 0x00000B89, 0x00000B8D, 0x00000B92,
	0x00000B96, 0x00000B9A, 0x00000B9F, 0x00000BA3, 0x00000BA7, 0x00000BAC, 0x00000BB0, 0x00000BB4,
	0x00000BB8, 0x00000BBD, 0x00000BC1, 0x00000BC5, 0x00000BCA, 0x00000BCE, 0x00000BD2, 0x00000BD6,
	0x00000BDA, 0x00000BDF, 0x00000BE3, 0x00000BE7, 0x00000BEB, 0x00000BEF, 0x00000BF4, 0x00000BF8,
	0x00000BFC, 0x00000C00, 0x00000C04, 0x00000C08, 0x00000C0D, 0x00000C11, 0x00000C15, 0x00000C19,
	0x00000C1D, 0x00000C21, 0x00000C25, 0x00000C29, 0x00000C2D, 0x00000C31, 0x00000C36, 0x00000C3A,
	0x00000C3E, 0x00000C42, 0x00000C46, 0x00000C4A, 0x00000C4E, 0x00000C52, 0x00000C56, 0x00000C5A,
	0x00000C5E, 0x00000C62, 0x00000C66, 0x00000C6A, 0x00000C6E, 0x00000C72, 0x00000C76, 0x00000C79,
	0x00000C7D, 0x00000C81, 0x00000C85, 0x00000C89, 0x00000C8D, 0x00000C91, 0x00000C95, 0x00000C99,
	0x00000C9D, 0x00000CA0, 0x00000CA4, 0x00000CA8, 0x00000CAC, 0x00000CB0, 0x00000CB4, 0x00000CB7,
	0x00000CBB, 0x00000CBF, 0x00000CC3, 0x00000CC7, 0x00000CCA, 0x00000CCE, 0x00000CD2, 0x00000CD6,
	0x00000CD9, 0x00000CDD, 0x00000CE1, 0x00000CE5, 0x00000CE8, 0x00000CEC, 0x00000CF0, 0x00000CF3,
	0x00000CF7, 0x00000CFB, 0x00000CFE, 0x00000D02, 0x00000D06, 0x00000D09, 0x00000D0D, 0x00000D11,
	0x00000D14, 0x00000D18, 0x00000D1C, 0x00000D1F, 0x00000D23, 0x00000D26, 0x00000D2A, 0x00000D2D,
	0x00000D31, 0x00000D35, 0x00000D38, 0x00000D3C, 0x00000D3F, 0x00000D43, 0x00000D46, 0x00000D4A,
	0x00000D4D, 0x00000D51, 0x00000D54, 0x00000D58, 0x00000D5B, 0x00000D5F, 0x00000D62, 0x00000D65,
	0x00000D69, 0x00000D6C, 0x00000D70, 0x00000D73, 0x00000D77, 0x00000D7A, 0x00000D7D, 0x00000D81,
	0x00000D84, 0x00000D87, 0x00000D8B, 0x00000D8E, 0x00000D91, 0x00000D95, 0x00000D98, 0x00000D9B,
	0x00000D9F, 0x00000DA2, 0x00000DA5, 0x00000DA9, 0x00000DAC, 0x00000DAF, 0x00000DB2, 0x00000DB6,
	0x00000DB9, 0x00000DBC, 0x00000DBF, 0x00000DC2, 0x00000DC6, 0x00000DC9, 0x00000DCC, 0x00000DCF,
	0x00000DD2, 0x00000DD5, 0x00000DD9, 0x00000DDC, 0x00000DDF, 0x00000DE2, 0x00000DE5, 0x00000DE8,
	0x00000DEB, 0x00000DEE, 0x00000DF2, 0x00000DF5, 0x00000DF8, 0x00000DFB, 0x00000DFE, 0x00000E01,
	0x00000E04, 0x00000E07, 0x00000E0A, 0x00000E0D, 0x00000E10, 0x00000E13, 0x00000E16, 0x00000E19,
	0x00000E1C, 0x00000E1F, 0x00000E22, 0x00000E25, 0x00000E28, 0x00000E2B, 0x00000E2D, 0x00000E30,
	0x00000E33, 0x00000E36, 0x00000E39, 0x00000E3C, 0x00000E3F, 0x00000E42, 0x00000E44, 0x00000E47,
	0x00000E4A, 0x00000E4D, 0x00000E50, 0x00000E53, 0x00000E55, 0x00000E58, 0x00000E5B, 0x00000E5E,
	0x00000E60, 0x00000E63, 0x00000E66, 0x00000E69, 0x00000E6B, 0x00000E6E, 0x00000E71, 0x00000E74,
	0x00000E76, 0x00000E79, 0x00000E7C, 0x00000E7E, 0x00000E81, 0x00000E84, 0x00000E86, 0x00000E89,
	0x00000E8B, 0x00000E8E, 0x00000E91, 0x00000E93, 0x00000E96, 0x00000E98, 0x00000E9B, 0x00000E9E,
	0x00000EA0, 0x00000EA3, 0x00000EA5, 0x00000EA8, 0x00000EAA, 0x00000EAD, 0x00000EAF, 0x00000EB2,
	0x00000EB4, 0x00000EB7, 0x00000EB9, 0x00000EBC, 0x00000EBE, 0x00000EC0, 0x00000EC3, 0x00000EC5,
	0x00000EC8, 0x00000ECA, 0x00000ECD, 0x00000ECF, 0x00000ED1, 0x00000ED4, 0x00000ED6, 0x00000ED8,
	0x00000EDB, 0x00000EDD, 0x00000EDF, 0x00000EE2, 0x00000EE4, 0x00000EE6, 0x00000EE8, 0x00000EEB,
	0x00000EED, 0x00000EEF, 0x00000EF2, 0x00000EF4, 0x00000EF6, 0x00000EF8, 0x00000EFA, 0x00000EFD,
	0x00000EFF, 0x00000F01, 0x00000F03, 0x00000F05, 0x00000F08, 0x00000F0A, 0x00000F0C, 0x00000F0E,
	0x00000F10, 0x00000F12, 0x00000F14, 0x00000F16, 0x00000F18, 0x00000F1B, 0x00000F1D, 0x00000F1F,
	0x00000F21, 0x00000F23, 0x00000F25, 0x00000F27, 0x00000F29, 0x00000F2B, 0x00000F2D, 0x00000F2F,
	0x00000F31, 0x00000F33, 0x00000F35, 0x00000F37, 0x00000F39, 0x00000F3B, 0x00000F3C, 0x00000F3E,
	0x00000F40, 0x00000F42, 0x00000F44, 0x00000F46, 0x00000F48, 0x00000F4A, 0x00000F4B, 0x00000F4D,
	0x00000F4F, 0x00000F51, 0x00000F53, 0x00000F55, 0x00000F56, 0x00000F58, 0x00000F5A, 0x00000F5C,
	0x00000F5D, 0x00000F5F, 0x00000F61, 0x00000F63, 0x00000F64, 0x00000F66, 0x00000F68, 0x00000F69,
	0x00000F6B, 0x00000F6D, 0x00000F6E, 0x00000F70, 0x00000F72, 0x00000F73, 0x00000F75, 0x00000F77,
	0x00000F78, 0x00000F7A, 0x00000F7B, 0x00000F7D, 0x00000F7F, 0x00000F80, 0x00000F82, 0x00000F83,
	0x00000F85, 0x00000F86, 0x00000F88, 0x00000F89, 0x00000F8B, 0x00000F8C, 0x00000F8E, 0x00000F8F,
	0x00000F91, 0x00000F92, 0x00000F94, 0x00000F95, 0x00000F96, 0x00000F98, 0x00000F99, 0x00000F9B,
	0x00000F9C, 0x00000F9D, 0x00000F9F, 0x00000FA0, 0x00000FA1, 0x00000FA3, 0x00000FA4, 0x00000FA5,
	0x00000FA7, 0x00000FA8, 0x00000FA9, 0x00000FAB, 0x00000FAC, 0x00000FAD, 0x00000FAE, 0x00000FB0,
	0x00000FB1, 0x00000FB2, 0x00000FB3, 0x00000FB4, 0x00000FB6, 0x00000FB7, 0x00000FB8, 0x00000FB9,
	0x00000FBA, 0x00000FBB, 0x00000FBD, 0x00000FBE, 0x00000FBF, 0x00000FC0, 0x00000FC1, 0x00000FC2,
	0x00000FC3, 0x00000FC4, 0x00000FC5, 0x00000FC6, 0x00000FC7, 0x00000FC8, 0x00000FC9, 0x00000FCA,
	0x00000FCB, 0x00000FCC, 0x00000FCD, 0x00000FCE, 0x00000FCF, 0x00000FD0, 0x00000FD1, 0x00000FD2,
	0x00000FD3, 0x00000FD4, 0x00000FD5, 0x00000FD6, 0x00000FD7, 0x00000FD8, 0x00000FD9, 0x00000FD9,
	0x00000FDA, 0x00000FDB, 0x00000FDC, 0x00000FDD, 0x00000FDE, 0x00000FDE, 0x00000FDF, 0x00000FE0,
	0x00000FE1, 0x00000FE1, 0x00000FE2, 0x00000FE3, 0x00000FE4, 0x00000FE4, 0x00000FE5, 0x00000FE6,
	0x00000FE7, 0x00000FE7, 0x00000FE8, 0x00000FE9, 0x00000FE9, 0x00000FEA, 0x00000FEB, 0x00000FEB,
	0x00000FEC, 0x00000FEC, 0x00000FED, 0x00000FEE, 0x00000FEE, 0x00000FEF, 0x00000FEF, 0x00000FF0,
	0x00000FF0, 0x00000FF1, 0x00000FF1, 0x00000FF2, 0x00000FF2, 0x00000FF3, 0x00000FF3, 0x00000FF4,
	0x00000FF4, 0x00000FF5, 0x00000FF5, 0x00000FF6, 0x00000FF6, 0x00000FF7, 0x00000FF7, 0x00000FF7,
	0x00000FF8, 0x00000FF8, 0x00000FF9, 0x00000FF9, 0x00000FF9, 0x00000FFA, 0x00000FFA, 0x00000FFA,
	0x00000FFB, 0x00000FFB, 0x00000FFB, 0x00000FFB, 0x00000FFC, 0x00000FFC, 0x00000FFC, 0x00000FFC,
	0x00000FFD, 0x00000FFD, 0x00000FFD, 0x00000FFD, 0x00000FFE, 0x00000FFE, 0x00000FFE, 0x00000FFE,
	0x00000FFE, 0x00000FFE, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF,
	0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF,
	0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF,
	0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFF, 0x00000FFE,
	0x00000FFE, 0x00000FFE, 0x00000FFE, 0x00000FFE, 0x00000FFE, 0x00000FFD, 0x00000FFD, 0x00000FFD,
	0x00000FFD, 0x00000FFC, 0x00000FFC, 0x00000FFC, 0x00000FFC, 0x00000FFB, 0x00000FFB, 0x00000FFB,
	0x00000FFB, 0x00000FFA, 0x00000FFA, 0x00000FFA, 0x00000FF9, 0x00000FF9, 0x00000FF9, 0x00000FF8,
	0x00000FF8, 0x00000FF7, 0x00000FF7, 0x00000FF7, 0x00000FF6, 0x00000FF6, 0x00000FF5, 0x00000FF5,
	0x00000FF4, 0x00000FF4, 0x00000FF3, 0x00000FF3, 0x00000FF2, 0x00000FF2, 0x00000FF1, 0x00000FF1,
	0x00000FF0, 0x00000FF0, 0x00000FEF, 0x00000FEF, 0x00000FEE, 0x00000FEE, 0x00000FED, 0x00000FEC,
	0x00000FEC, 0x00000FEB, 0x00000FEB, 0x00000FEA, 0x00000FE9, 0x00000FE9, 0x00000FE8, 0x00000FE7,
	0x00000FE7, 0x00000FE6, 0x00000FE5, 0x00000FE4, 0x00000FE4, 0x00000FE3, 0x00000FE2, 0x00000FE1,
	0x00000FE1, 0x00000FE0, 0x00000FDF, 0x00000FDE, 0x00000FDE, 0x00000FDD, 0x00000FDC, 0x00000FDB,
	0x00000FDA, 0x00000FD9, 0x00000FD9, 0x00000FD8, 0x00000FD7, 0x00000FD6, 0x00000FD5, 0x00000FD4,
	0x00000FD3, 0x00000FD2, 0x00000FD1, 0x00000FD0, 0x00000FCF, 0x00000FCE, 0x00000FCD, 0x00000FCC,
	0x00000FCB, 0x00000FCA, 0x00000FC9, 0x00000FC8, 0x00000FC7, 0x00000FC6, 0x00000FC5, 0x00000FC4,
	0x00000FC3, 0x00000FC2, 0x00000FC1, 0x00000FC0, 0x00000FBF, 0x00000FBE, 0x00000FBD, 0x00000FBB,
	0x00000FBA, 0x00000FB9, 0x00000FB8, 0x00000FB7, 0x00000FB6, 0x00000FB4, 0x00000FB3, 0x00000FB2,
	0x00000FB1, 0x00000FB0, 0x00000FAE, 0x00000FAD, 0x00000FAC, 0x00000FAB, 0x00000FA9, 0x00000FA8,
	0x00000FA7, 0x00000FA5, 0x00000FA4, 0x00000FA3, 0x00000FA1, 0x00000FA0, 0x00000F9F, 0x00000F9D,
	0x00000F9C, 0x00000F9B, 0x00000F99, 0x00000F98, 0x00000F96, 0x00000F95, 0x00000F94, 0x00000F92,
	0x00000F91, 0x00000F8F, 0x00000F8E, 0x00000F8C, 0x00000F8B, 0x00000F89, 0x00000F88, 0x00000F86,
	0x00000F85, 0x00000F83, 0x00000F82, 0x00000F80, 0x00000F7F, 0x00000F7D, 0x00000F7B, 0x00000F7A,
	0x00000F78, 0x00000F77, 0x00000F75, 0x00000F73, 0x00000F72, 0x00000F70, 0x00000F6E, 0x00000F6D,
	0x00000F6B, 0x00000F69, 0x00000F68, 0x00000F66, 0x00000F64, 0x00000F63, 0x00000F61, 0x00000F5F,
	0x00000F5D, 0x00000F5C, 0x00000F5A, 0x00000F58, 0x00000F56, 0x00000F55, 0x00000F53, 0x00000F51,
	0x00000F4F, 0x00000F4D, 0x00000F4B, 0x00000F4A, 0x00000F48, 0x00000F46, 0x00000F44, 0x00000F42,
	0x00000F40, 0x00000F3E, 0x00000F3C, 0x00000F3B, 0x00000F39, 0x00000F37, 0x00000F35, 0x00000F33,
	0x00000F31, 0x00000F2F, 0x00000F2D, 0x00000F2B, 0x00000F29, 0x00000F27, 0x00000F25, 0x00000F23,
	0x00000F21, 0x00000F1F, 0x00000F1D, 0x00000F1B, 0x00000F18, 0x00000F16, 0x00000F14, 0x00000F12,
	0x00000F10, 0x00000F0E, 0x00000F0C, 0x00000F0A, 0x00000F08, 0x00000F05, 0x00000F03, 0x00000F01,
	0x00000EFF, 0x00000EFD, 0x00000EFA, 0x00000EF8, 0x00000EF6, 0x00000EF4, 0x00000EF2, 0x00000EEF,
	0x00000EED, 0x00000EEB, 0x00000EE8, 0x00000EE6, 0x00000EE4, 0x00000EE2, 0x00000EDF, 0x00000EDD,
	0x00000EDB, 0x00000ED8, 0x00000ED6, 0x00000ED4, 0x00000ED1, 0x00000ECF, 0x00000ECD, 0x00000ECA,
	0x00000EC8, 0x00000EC5, 0x00000EC3, 0x00000EC0, 0x00000EBE, 0x00000EBC, 0x00000EB9, 0x00000EB7,
	0x00000EB4, 0x00000EB2, 0x00000EAF, 0x00000EAD, 0x00000EAA, 0x00000EA8, 0x00000EA5, 0x00000EA3,
	0x00000EA0, 0x00000E9E, 0x00000E9B, 0x00000E98, 0x00000E96, 0x00000E93, 0x00000E91, 0x00000E8E,
	0x00000E8B, 0x00000E89, 0x00000E86, 0x00000E84, 0x00000E81, 0x00000E7E, 0x00000E7C, 0x00000E79,
	0x00000E76, 0x00000E74, 0x00000E71, 0x00000E6E, 0x00000E6B, 0x00000E69, 0x00000E66, 0x00000E63,
	0x00000E60, 0x00000E5E, 0x00000E5B, 0x00000E58, 0x00000E55, 0x00000E53, 0x00000E50, 0x00000E4D,
	0x00000E4A, 0x00000E47, 0x00000E44, 0x00000E42, 0x00000E3F, 0x00000E3C, 0x00000E39, 0x00000E36,
	0x00000E33, 0x00000E30, 0x00000E2D, 0x00000E2B, 0x00000E28, 0x00000E25, 0x00000E22, 0x00000E1F,
	0x00000E1C, 0x00000E19, 0x00000E16, 0x00000E13, 0x00000E10, 0x00000E0D, 0x00000E0A, 0x00000E07,
	0x00000E04, 0x00000E01, 0x00000DFE, 0x00000DFB, 0x00000DF8, 0x00000DF5, 0x00000DF2, 0x00000DEE,
	0x00000DEB, 0x00000DE8, 0x00000DE5, 0x00000DE2, 0x00000DDF, 0x00000DDC, 0x00000DD9, 0x00000DD5,
	0x00000DD2, 0x00000DCF, 0x00000DCC, 0x00000DC9, 0x00000DC6, 0x00000DC2, 0x00000DBF, 0x00000DBC,
	0x00000DB9, 0x00000DB6, 0x00000DB2, 0x00000DAF, 0x00000DAC, 0x00000DA9, 0x00000DA5, 0x00000DA2,
	0x00000D9F, 0x00000D9B, 0x00000D98, 0x00000D95, 0x00000D91, 0x00000D8E, 0x00000D8B, 0x00000D87,
	0x00000D84, 0x00000D81, 0x00000D7D, 0x00000D7A, 0x00000D77, 0x00000D73, 0x00000D70, 0x00000D6C,
	0x00000D69, 0x00000D65, 0x00000D62, 0x00000D5F, 0x00000D5B, 0x00000D58, 0x00000D54, 0x00000D51,
	0x00000D4D, 0x00000D4A, 0x00000D46, 0x00000D43, 0x00000D3F, 0x00000D3C, 0x00000D38, 0x00000D35,
	0x00000D31, 0x00000D2D, 0x00000D2A, 0x00000D26, 0x00000D23, 0x00000D1F, 0x00000D1C, 0x00000D18,
	0x00000D14, 0x00000D11, 0x00000D0D, 0x00000D09, 0x00000D06, 0x00000D02, 0x00000CFE, 0x00000CFB,
	0x00000CF7, 0x00000CF3, 0x00000CF0, 0x00000CEC, 0x00000CE8, 0x00000CE5, 0x00000CE1, 0x00000CDD,
	0x00000CD9, 0x00000CD6, 0x00000CD2, 0x00000CCE, 0x00000CCA, 0x00000CC7, 0x00000CC3, 0x00000CBF,
	0x00000CBB, 0x00000CB7, 0x00000CB4, 0x00000CB0, 0x00000CAC, 0x00000CA8, 0x00000CA4, 0x00000CA0,
	0x00000C9D, 0x00000C99, 0x00000C95, 0x00000C91, 0x00000C8D, 0x00000C89, 0x00000C85, 0x00000C81,
	0x00000C7D, 0x00000C79, 0x00000C76, 0x00000C72, 0x00000C6E, 0x00000C6A, 0x00000C66, 0x00000C62,
	0x00000C5E, 0x00000C5A, 0x00000C56, 0x00000C52, 0x00000C4E, 0x00000C4A, 0x00000C46, 0x00000C42,
	0x00000C3E, 0x00000C3A, 0x00000C36, 0x00000C31, 0x00000C2D, 0x00000C29, 0x00000C25, 0x00000C21,
	0x00000C1D, 0x00000C19, 0x00000C15, 0x00000C11, 0x00000C0D, 0x00000C08, 0x00000C04, 0x00000C00,
	0x00000BFC, 0x00000BF8, 0x00000BF4, 0x00000BEF, 0x00000BEB, 0x00000BE7, 0x00000BE3, 0x00000BDF,
	0x00000BDA, 0x00000BD6, 0x00000BD2, 0x00000BCE, 0x00000BCA, 0x00000BC5, 0x00000BC1, 0x00000BBD,
	0x00000BB8, 0x00000BB4, 0x00000BB0, 0x00000BAC, 0x00000BA7, 0x00000BA3, 0x00000B9F, 0x00000B9A,
	0x00000B96, 0x00000B92, 0x00000B8D, 0x00000B89, 0x00000B85, 0x00000B80, 0x00000B7C, 0x00000B78,
	0x00000B73, 0x00000B6F, 0x00000B6A, 0x00000B66, 0x00000B62, 0x00000B5D, 0x00000B59, 0x00000B54,
	0x00000B50, 0x00000B4B, 0x00000B47, 0x00000B42, 0x00000B3E, 0x00000B3A, 0x00000B35, 0x00000B31,
	0x00000B2C, 0x00000B28, 0x00000B23, 0x00000B1F, 0x00000B1A, 0x00000B15, 0x00000B11, 0x00000B0C,
	0x00000B08, 0x00000B03, 0x00000AFF, 0x00000AFA, 0x00000AF6, 0x00000AF1, 0x00000AEC, 0x00000AE8,
	0x00000AE3, 0x00000ADF, 0x00000ADA, 0x00000AD5, 0x00000AD1, 0x00000ACC, 0x00000AC8, 0x00000AC3,
	0x00000ABE, 0x00000ABA, 0x00000AB5, 0x00000AB0, 0x00000AAC, 0x00000AA7, 0x00000AA2, 0x00000A9D,
	0x00000A99, 0x00000A94, 0x00000A8F, 0x00000A8B, 0x00000A86, 0x00000A81, 0x00000A7C, 0x00000A78,
	0x00000A73, 0x00000A6E, 0x00000A69, 0x00000A65, 0x00000A60, 0x00000A5B, 0x00000A56, 0x00000A51,
	0x00000A4D, 0x00000A48, 0x00000A43, 0x00000A3E, 0x00000A39, 0x00000A35, 0x00000A30, 0x00000A2B,
	0x00000A26, 0x00000A21, 0x00000A1C, 0x00000A17, 0x00000A12, 0x00000A0E, 0x00000A09, 0x00000A04,
	0x000009FF, 0x000009FA, 0x000009F5, 0x000009F0, 0x000009EB, 0x000009E6, 0x000009E1, 0x000009DC,
	0x000009D7, 0x000009D3, 0x000009CE, 0x000009C9, 0x000009C4, 0x000009BF, 0x000009BA, 0x000009B5,
	0x000009B0, 0x000009AB, 0x000009A6, 0x000009A1, 0x0000099C, 0x00000997, 0x00000992, 0x0000098D,
	0x00000987, 0x00000982, 0x0000097D, 0x00000978, 0x00000973, 0x0000096E, 0x00000969, 0x00000964,
	0x0000095F, 0x0000095A, 0x00000955, 0x00000950, 0x0000094B, 0x00000945, 0x00000940, 0x0000093B,
	0x00000936, 0x00000931, 0x0000092C, 0x00000927, 0x00000921, 0x0000091C, 0x00000917, 0x00000912,
	0x0000090D, 0x00000908, 0x00000902, 0x000008FD, 0x000008F8, 0x000008F3, 0x000008EE, 0x000008E8,
	0x000008E3, 0x000008DE, 0x000008D9, 0x000008D3, 0x000008CE, 0x000008C9, 0x000008C4, 0x000008BE,
	0x000008B9, 0x000008B4, 0x000008AF, 0x000008A9, 0x000008A4, 0x0000089F, 0x00000899, 0x00000894,
	0x0000088F, 0x0000088A, 0x00000884, 0x0000087F, 0x0000087A, 0x00000874, 0x0000086F, 0x0000086A,
	0x00000864, 0x0000085F, 0x0000085A, 0x00000854, 0x0000084F, 0x00000849, 0x00000844, 0x0000083F,
	0x00000839, 0x00000834, 0x0000082E, 0x00000829, 0x00000824, 0x0000081E, 0x00000819, 0x00000813,
	0x0000080E, 0x00000809, 0x00000803, 0x000007FE, 0x000007F8, 0x000007F3, 0x000007ED, 0x000007E8,
	0x000007E2, 0x000007DD, 0x000007D7, 0x000007D2, 0x000007CD, 0x000007C7, 0x000007C2, 0x000007BC,
	0x000007B7, 0x000007B1, 0x000007AC, 0x000007A6, 0x000007A0, 0x0000079B, 0x00000795, 0x00000790,
	0x0000078A, 0x00000785, 0x0000077F, 0x0000077A, 0x00000774, 0x0000076F, 0x00000769, 0x00000763,
	0x0000075E, 0x00000758, 0x00000753, 0x0000074D, 0x00000748, 0x00000742, 0x0000073C, 0x00000737,
	0x00000731, 0x0000072B, 0x00000726, 0x00000720, 0x0000071B, 0x00000715, 0x0000070F, 0x0000070A,
	0x00000704, 0x000006FE, 0x000006F9, 0x000006F3, 0x000006ED, 0x000006E8, 0x000006E2, 0x000006DC,
	0x000006D7, 0x000006D1, 0x000006CB, 0x000006C6, 0x000006C0, 0x000006BA, 0x000006B5, 0x000006AF,
	0x000006A9, 0x000006A3, 0x0000069E, 0x00000698, 0x00000692, 0x0000068D, 0x00000687, 0x00000681,
	0x0000067B, 0x00000676, 0x00000670, 0x0000066A, 0x00000664, 0x0000065F, 0x00000659, 0x00000653,
	0x0000064D, 0x00000648, 0x00000642, 0x0000063C, 0x00000636, 0x00000630, 0x0000062B, 0x00000625,
	0x0000061F, 0x00000619, 0x00000613, 0x0000060E, 0x00000608, 0x00000602, 0x000005FC, 0x000005F6,
	0x000005F0, 0x000005EB, 0x000005E5, 0x000005DF, 0x000005D9, 0x000005D3, 0x000005CD, 0x000005C7,
	0x000005C2, 0x000005BC, 0x000005B6, 0x000005B0, 0x000005AA, 0x000005A4, 0x0000059E, 0x00000599,
	0x00000593, 0x0000058D, 0x00000587, 0x00000581, 0x0000057B, 0x00000575, 0x0000056F, 0x00000569,
	0x00000563, 0x0000055D, 0x00000558, 0x00000552, 0x0000054C, 0x00000546, 0x00000540, 0x0000053A,
	0x00000534, 0x0000052E, 0x00000528, 0x00000522, 0x0000051C, 0x00000516, 0x00000510, 0x0000050A,
	0x00000504, 0x000004FE, 0x000004F8, 0x000004F2, 0x000004EC, 0x000004E6, 0x000004E0, 0x000004DB,
	0x000004D5, 0x000004CF, 0x000004C9, 0x000004C3, 0x000004BD, 0x000004B7, 0x000004B1, 0x000004AB,
	0x000004A5, 0x0000049E, 0x00000498, 0x00000492, 0x0000048C, 0x00000486, 0x00000480, 0x0000047A,
	0x00000474, 0x0000046E, 0x00000468, 0x00000462, 0x0000045C, 0x00000456, 0x00000450, 0x0000044A,
	0x00000444, 0x0000043E, 0x00000438, 0x00000432, 0x0000042C, 0x00000426, 0x00000420, 0x0000041A,
	0x00000413, 0x0000040D, 0x00000407, 0x00000401, 0x000003FB, 0x000003F5, 0x000003EF, 0x000003E9,
	0x000003E3, 0x000003DD, 0x000003D7, 0x000003D0, 0x000003CA, 0x000003C4, 0x000003BE, 0x000003B8,
	0x000003B2, 0x000003AC, 0x000003A6, 0x000003A0, 0x00000399, 0x00000393, 0x0000038D, 0x00000387,
	0x00000381, 0x0000037B, 0x00000375, 0x0000036F, 0x00000368, 0x00000362, 0x0000035C, 0x00000356,
	0x00000350, 0x0000034A, 0x00000344, 0x0000033D, 0x00000337, 0x00000331, 0x0000032B, 0x00000325,
	0x0000031F, 0x00000318, 0x00000312, 0x0000030C, 0x00000306, 0x00000300, 0x000002FA, 0x000002F3,
	0x000002ED, 0x000002E7, 0x000002E1, 0x000002DB, 0x000002D5, 0x000002CE, 0x000002C8, 0x000002C2,
	0x000002BC, 0x000002B6, 0x000002AF, 0x000002A9, 0x000002A3, 0x0000029D, 0x00000297, 0x00000290,
	0x0000028A, 0x00000284, 0x0000027E, 0x00000278, 0x00000271, 0x0000026B, 0x00000265, 0x0000025F,
	0x00000259, 0x00000252, 0x0000024C, 0x00000246, 0x00000240, 0x00000239, 0x00000233, 0x0000022D,
	0x00000227, 0x00000221, 0x0000021A, 0x00000214, 0x0000020E, 0x00000208, 0x00000201, 0x000001FB,
	0x000001F5, 0x000001EF, 0x000001E8, 0x000001E2, 0x000001DC, 0x000001D6, 0x000001CF, 0x000001C9,
	0x000001C3, 0x000001BD, 0x000001B6, 0x000001B0, 0x000001AA, 0x000001A4, 0x0000019D, 0x00000197,
	0x00000191, 0x0000018B, 0x00000184, 0x0000017E, 0x00000178, 0x00000172, 0x0000016B, 0x00000165,
	0x0000015F, 0x00000159, 0x00000152, 0x0000014C, 0x00000146, 0x00000140, 0x00000139, 0x00000133,
	0x0000012D, 0x00000127, 0x00000120, 0x0000011A, 0x00000114, 0x0000010D, 0x00000107, 0x00000101,
	0x000000FB, 0x000000F4, 0x000000EE, 0x000000E8, 0x000000E2, 0x000000DB, 0x000000D5, 0x000000CF,
	0x000000C8, 0x000000C2, 0x000000BC, 0x000000B6, 0x000000AF, 0x000000A9, 0x000000A3, 0x0000009D,
	0x00000096, 0x00000090, 0x0000008A, 0x00000083, 0x0000007D, 0x00000077, 0x00000071, 0x0000006A,
	0x00000064, 0x0000005E, 0x00000057, 0x00000051, 0x0000004B, 0x00000045, 0x0000003E, 0x00000038,
	0x00000032, 0x0000002B, 0x00000025, 0x0000001F, 0x00000019, 0x00000012, 0x0000000C, 0x00000006,
	0x00000000, 0xFFFFFFFA, 0xFFFFFFF4, 0xFFFFFFEE, 0xFFFFFFE7, 0xFFFFFFE1, 0xFFFFFFDB, 0xFFFFFFD5,
	0xFFFFFFCE, 0xFFFFFFC8, 0xFFFFFFC2, 0xFFFFFFBB, 0xFFFFFFB5, 0xFFFFFFAF, 0xFFFFFFA9, 0xFFFFFFA2,
	0xFFFFFF9C, 0xFFFFFF96, 0xFFFFFF8F, 0xFFFFFF89, 0xFFFFFF83, 0xFFFFFF7D, 0xFFFFFF76, 0xFFFFFF70,
	0xFFFFFF6A, 0xFFFFFF63, 0xFFFFFF5D, 0xFFFFFF57, 0xFFFFFF51, 0xFFFFFF4A, 0xFFFFFF44, 0xFFFFFF3E,
	0xFFFFFF38, 0xFFFFFF31, 0xFFFFFF2B, 0xFFFFFF25, 0xFFFFFF1E, 0xFFFFFF18, 0xFFFFFF12, 0xFFFFFF0C,
	0xFFFFFF05, 0xFFFFFEFF, 0xFFFFFEF9, 0xFFFFFEF3, 0xFFFFFEEC, 0xFFFFFEE6, 0xFFFFFEE0, 0xFFFFFED9,
	0xFFFFFED3, 0xFFFFFECD, 0xFFFFFEC7, 0xFFFFFEC0, 0xFFFFFEBA, 0xFFFFFEB4, 0xFFFFFEAE, 0xFFFFFEA7,
	0xFFFFFEA1, 0xFFFFFE9B, 0xFFFFFE95, 0xFFFFFE8E, 0xFFFFFE88, 0xFFFFFE82, 0xFFFFFE7C, 0xFFFFFE75,
	0xFFFFFE6F, 0xFFFFFE69, 0xFFFFFE63, 0xFFFFFE5C, 0xFFFFFE56, 0xFFFFFE50, 0xFFFFFE4A, 0xFFFFFE43,
	0xFFFFFE3D, 0xFFFFFE37, 0xFFFFFE31, 0xFFFFFE2A, 0xFFFFFE24, 0xFFFFFE1E, 0xFFFFFE18, 0xFFFFFE11,
	0xFFFFFE0B, 0xFFFFFE05, 0xFFFFFDFF, 0xFFFFFDF8, 0xFFFFFDF2, 0xFFFFFDEC, 0xFFFFFDE6, 0xFFFFFDDF,
	0xFFFFFDD9, 0xFFFFFDD3, 0xFFFFFDCD, 0xFFFFFDC7, 0xFFFFFDC0, 0xFFFFFDBA, 0xFFFFFDB4, 0xFFFFFDAE,
	0xFFFFFDA7, 0xFFFFFDA1, 0xFFFFFD9B, 0xFFFFFD95, 0xFFFFFD8F, 0xFFFFFD88, 0xFFFFFD82, 0xFFFFFD7C,
	0xFFFFFD76, 0xFFFFFD70, 0xFFFFFD69, 0xFFFFFD63, 0xFFFFFD5D, 0xFFFFFD57, 0xFFFFFD51, 0xFFFFFD4A,
	0xFFFFFD44, 0xFFFFFD3E, 0xFFFFFD38, 0xFFFFFD32, 0xFFFFFD2B, 0xFFFFFD25, 0xFFFFFD1F, 0xFFFFFD19,
	0xFFFFFD13, 0xFFFFFD0D, 0xFFFFFD06, 0xFFFFFD00, 0xFFFFFCFA, 0xFFFFFCF4, 0xFFFFFCEE, 0xFFFFFCE8,
	0xFFFFFCE1, 0xFFFFFCDB, 0xFFFFFCD5, 0xFFFFFCCF, 0xFFFFFCC9, 0xFFFFFCC3, 0xFFFFFCBC, 0xFFFFFCB6,
	0xFFFFFCB0, 0xFFFFFCAA, 0xFFFFFCA4, 0xFFFFFC9E, 0xFFFFFC98, 0xFFFFFC91, 0xFFFFFC8B, 0xFFFFFC85,
	0xFFFFFC7F, 0xFFFFFC79, 0xFFFFFC73, 0xFFFFFC6D, 0xFFFFFC67, 0xFFFFFC60, 0xFFFFFC5A, 0xFFFFFC54,
	0xFFFFFC4E, 0xFFFFFC48, 0xFFFFFC42, 0xFFFFFC3C, 0xFFFFFC36, 0xFFFFFC30, 0xFFFFFC29, 0xFFFFFC23,
	0xFFFFFC1D, 0xFFFFFC17, 0xFFFFFC11, 0xFFFFFC0B, 0xFFFFFC05, 0xFFFFFBFF, 0xFFFFFBF9, 0xFFFFFBF3,
	0xFFFFFBED, 0xFFFFFBE6, 0xFFFFFBE0, 0xFFFFFBDA, 0xFFFFFBD4, 0xFFFFFBCE, 0xFFFFFBC8, 0xFFFFFBC2,
	0xFFFFFBBC, 0xFFFFFBB6, 0xFFFFFBB0, 0xFFFFFBAA, 0xFFFFFBA4, 0xFFFFFB9E, 0xFFFFFB98, 0xFFFFFB92,
	0xFFFFFB8C, 0xFFFFFB86, 0xFFFFFB80, 0xFFFFFB7A, 0xFFFFFB74, 0xFFFFFB6E, 0xFFFFFB68, 0xFFFFFB62,
	0xFFFFFB5B, 0xFFFFFB55, 0xFFFFFB4F, 0xFFFFFB49, 0xFFFFFB43, 0xFFFFFB3D, 0xFFFFFB37, 0xFFFFFB31,
	0xFFFFFB2B, 0xFFFFFB25, 0xFFFFFB20, 0xFFFFFB1A, 0xFFFFFB14, 0xFFFFFB0E, 0xFFFFFB08, 0xFFFFFB02,
	0xFFFFFAFC, 0xFFFFFAF6, 0xFFFFFAF0, 0xFFFFFAEA, 0xFFFFFAE4, 0xFFFFFADE, 0xFFFFFAD8, 0xFFFFFAD2,
	0xFFFFFACC, 0xFFFFFAC6, 0xFFFFFAC0, 0xFFFFFABA, 0xFFFFFAB4, 0xFFFFFAAE, 0xFFFFFAA8, 0xFFFFFAA3,
	0xFFFFFA9D, 0xFFFFFA97, 0xFFFFFA91, 0xFFFFFA8B, 0xFFFFFA85, 0xFFFFFA7F, 0xFFFFFA79, 0xFFFFFA73,
	0xFFFFFA6D, 0xFFFFFA67, 0xFFFFFA62, 0xFFFFFA5C, 0xFFFFFA56, 0xFFFFFA50, 0xFFFFFA4A, 0xFFFFFA44,
	0xFFFFFA3E, 0xFFFFFA39, 0xFFFFFA33, 0xFFFFFA2D, 0xFFFFFA27, 0xFFFFFA21, 0xFFFFFA1B, 0xFFFFFA15,
	0xFFFFFA10, 0xFFFFFA0A, 0xFFFFFA04, 0xFFFFF9FE, 0xFFFFF9F8, 0xFFFFF9F2, 0xFFFFF9ED, 0xFFFFF9E7,
	0xFFFFF9E1, 0xFFFFF9DB, 0xFFFFF9D5, 0xFFFFF9D0, 0xFFFFF9CA, 0xFFFFF9C4, 0xFFFFF9BE, 0xFFFFF9B8,
	0xFFFFF9B3, 0xFFFFF9AD, 0xFFFFF9A7, 0xFFFFF9A1, 0xFFFFF99C, 0xFFFFF996, 0xFFFFF990, 0xFFFFF98A,
	0xFFFFF985, 0xFFFFF97F, 0xFFFFF979, 0xFFFFF973, 0xFFFFF96E, 0xFFFFF968, 0xFFFFF962, 0xFFFFF95D,
	0xFFFFF957, 0xFFFFF951, 0xFFFFF94B, 0xFFFFF946, 0xFFFFF940, 0xFFFFF93A, 0xFFFFF935, 0xFFFFF92F,
	0xFFFFF929, 0xFFFFF924, 0xFFFFF91E, 0xFFFFF918, 0xFFFFF913, 0xFFFFF90D, 0xFFFFF907, 0xFFFFF902,
	0xFFFFF8FC, 0xFFFFF8F6, 0xFFFFF8F1, 0xFFFFF8EB, 0xFFFFF8E5, 0xFFFFF8E0, 0xFFFFF8DA, 0xFFFFF8D5,
	0xFFFFF8CF, 0xFFFFF8C9, 0xFFFFF8C4, 0xFFFFF8BE, 0xFFFFF8B8, 0xFFFFF8B3, 0xFFFFF8AD, 0xFFFFF8A8,
	0xFFFFF8A2, 0xFFFFF89D, 0xFFFFF897, 0xFFFFF891, 0xFFFFF88C, 0xFFFFF886, 0xFFFFF881, 0xFFFFF87B,
	0xFFFFF876, 0xFFFFF870, 0xFFFFF86B, 0xFFFFF865, 0xFFFFF860, 0xFFFFF85A, 0xFFFFF854, 0xFFFFF84F,
	0xFFFFF849, 0xFFFFF844, 0xFFFFF83E, 0xFFFFF839, 0xFFFFF833, 0xFFFFF82E, 0xFFFFF829, 0xFFFFF823,
	0xFFFFF81E, 0xFFFFF818, 0xFFFFF813, 0xFFFFF80D, 0xFFFFF808, 0xFFFFF802, 0xFFFFF7FD, 0xFFFFF7F7,
	0xFFFFF7F2, 0xFFFFF7ED, 0xFFFFF7E7, 0xFFFFF7E2, 0xFFFFF7DC, 0xFFFFF7D7, 0xFFFFF7D2, 0xFFFFF7CC,
	0xFFFFF7C7, 0xFFFFF7C1, 0xFFFFF7BC, 0xFFFFF7B7, 0xFFFFF7B1, 0xFFFFF7AC, 0xFFFFF7A6, 0xFFFFF7A1,
	0xFFFFF79C, 0xFFFFF796, 0xFFFFF791, 0xFFFFF78C, 0xFFFFF786, 0xFFFFF781, 0xFFFFF77C, 0xFFFFF776,
	0xFFFFF771, 0xFFFFF76C, 0xFFFFF767, 0xFFFFF761, 0xFFFFF75C, 0xFFFFF757, 0xFFFFF751, 0xFFFFF74C,
	0xFFFFF747, 0xFFFFF742, 0xFFFFF73C, 0xFFFFF737, 0xFFFFF732, 0xFFFFF72D, 0xFFFFF727, 0xFFFFF722,
	0xFFFFF71D, 0xFFFFF718, 0xFFFFF712, 0xFFFFF70D, 0xFFFFF708, 0xFFFFF703, 0xFFFFF6FE, 0xFFFFF6F8,
	0xFFFFF6F3, 0xFFFFF6EE, 0xFFFFF6E9, 0xFFFFF6E4, 0xFFFFF6DF, 0xFFFFF6D9, 0xFFFFF6D4, 0xFFFFF6CF,
	0xFFFFF6CA, 0xFFFFF6C5, 0xFFFFF6C0, 0xFFFFF6BB, 0xFFFFF6B5, 0xFFFFF6B0, 0xFFFFF6AB, 0xFFFFF6A6,
	0xFFFFF6A1, 0xFFFFF69C, 0xFFFFF697, 0xFFFFF692, 0xFFFFF68D, 0xFFFFF688, 0xFFFFF683, 0xFFFFF67E,
	0xFFFFF679, 0xFFFFF673, 0xFFFFF66E, 0xFFFFF669, 0xFFFFF664, 0xFFFFF65F, 0xFFFFF65A, 0xFFFFF655,
	0xFFFFF650, 0xFFFFF64B, 0xFFFFF646, 0xFFFFF641, 0xFFFFF63C, 0xFFFFF637, 0xFFFFF632, 0xFFFFF62D,
	0xFFFFF629, 0xFFFFF624, 0xFFFFF61F, 0xFFFFF61A, 0xFFFFF615, 0xFFFFF610, 0xFFFFF60B, 0xFFFFF606,
	0xFFFFF601, 0xFFFFF5FC, 0xFFFFF5F7, 0xFFFFF5F2, 0xFFFFF5EE, 0xFFFFF5E9, 0xFFFFF5E4, 0xFFFFF5DF,
	0xFFFFF5DA, 0xFFFFF5D5, 0xFFFFF5D0, 0xFFFFF5CB, 0xFFFFF5C7, 0xFFFFF5C2, 0xFFFFF5BD, 0xFFFFF5B8,
	0xFFFFF5B3, 0xFFFFF5AF, 0xFFFFF5AA, 0xFFFFF5A5, 0xFFFFF5A0, 0xFFFFF59B, 0xFFFFF597, 0xFFFFF592,
	0xFFFFF58D, 0xFFFFF588, 0xFFFFF584, 0xFFFFF57F, 0xFFFFF57A, 0xFFFFF575, 0xFFFFF571, 0xFFFFF56C,
	0xFFFFF567, 0xFFFFF563, 0xFFFFF55E, 0xFFFFF559, 0xFFFFF554, 0xFFFFF550, 0xFFFFF54B, 0xFFFFF546,
	0xFFFFF542, 0xFFFFF53D, 0xFFFFF538, 0xFFFFF534, 0xFFFFF52F, 0xFFFFF52B, 0xFFFFF526, 0xFFFFF521,
	0xFFFFF51D, 0xFFFFF518, 0xFFFFF514, 0xFFFFF50F, 0xFFFFF50A, 0xFFFFF506, 0xFFFFF501, 0xFFFFF4FD,
	0xFFFFF4F8, 0xFFFFF4F4, 0xFFFFF4EF, 0xFFFFF4EB, 0xFFFFF4E6, 0xFFFFF4E1, 0xFFFFF4DD, 0xFFFFF4D8,
	0xFFFFF4D4, 0xFFFFF4CF, 0xFFFFF4CB, 0xFFFFF4C6, 0xFFFFF4C2, 0xFFFFF4BE, 0xFFFFF4B9, 0xFFFFF4B5,
	0xFFFFF4B0, 0xFFFFF4AC, 0xFFFFF4A7, 0xFFFFF4A3, 0xFFFFF49E, 0xFFFFF49A, 0xFFFFF496, 0xFFFFF491,
	0xFFFFF48D, 0xFFFFF488, 0xFFFFF484, 0xFFFFF480, 0xFFFFF47B, 0xFFFFF477, 0xFFFFF473, 0xFFFFF46E,
	0xFFFFF46A, 0xFFFFF466, 0xFFFFF461, 0xFFFFF45D, 0xFFFFF459, 0xFFFFF454, 0xFFFFF450, 0xFFFFF44C,
	0xFFFFF448, 0xFFFFF443, 0xFFFFF43F, 0xFFFFF43B, 0xFFFFF436, 0xFFFFF432, 0xFFFFF42E, 0xFFFFF42A,
	0xFFFFF426, 0xFFFFF421, 0xFFFFF41D, 0xFFFFF419, 0xFFFFF415, 0xFFFFF411, 0xFFFFF40C, 0xFFFFF408,
	0xFFFFF404, 0xFFFFF400, 0xFFFFF3FC, 0xFFFFF3F8, 0xFFFFF3F3, 0xFFFFF3EF, 0xFFFFF3EB, 0xFFFFF3E7,
	0xFFFFF3E3, 0xFFFFF3DF, 0xFFFFF3DB, 0xFFFFF3D7, 0xFFFFF3D3, 0xFFFFF3CF, 0xFFFFF3CA, 0xFFFFF3C6,
	0xFFFFF3C2, 0xFFFFF3BE, 0xFFFFF3BA, 0xFFFFF3B6, 0xFFFFF3B2, 0xFFFFF3AE, 0xFFFFF3AA, 0xFFFFF3A6,
	0xFFFFF3A2, 0xFFFFF39E, 0xFFFFF39A, 0xFFFFF396, 0xFFFFF392, 0xFFFFF38E, 0xFFFFF38A, 0xFFFFF387,
	0xFFFFF383, 0xFFFFF37F, 0xFFFFF37B, 0xFFFFF377, 0xFFFFF373, 0xFFFFF36F, 0xFFFFF36B, 0xFFFFF367,
	0xFFFFF363, 0xFFFFF360, 0xFFFFF35C, 0xFFFFF358, 0xFFFFF354, 0xFFFFF350, 0xFFFFF34C, 0xFFFFF349,
	0xFFFFF345, 0xFFFFF341, 0xFFFFF33D, 0xFFFFF339, 0xFFFFF336, 0xFFFFF332, 0xFFFFF32E, 0xFFFFF32A,
	0xFFFFF327, 0xFFFFF323, 0xFFFFF31F, 0xFFFFF31B, 0xFFFFF318, 0xFFFFF314, 0xFFFFF310, 0xFFFFF30D,
	0xFFFFF309, 0xFFFFF305, 0xFFFFF302, 0xFFFFF2FE, 0xFFFFF2FA, 0xFFFFF2F7, 0xFFFFF2F3, 0xFFFFF2EF,
	0xFFFFF2EC, 0xFFFFF2E8, 0xFFFFF2E4, 0xFFFFF2E1, 0xFFFFF2DD, 0xFFFFF2DA, 0xFFFFF2D6, 0xFFFFF2D3,
	0xFFFFF2CF, 0xFFFFF2CB, 0xFFFFF2C8, 0xFFFFF2C4, 0xFFFFF2C1, 0xFFFFF2BD, 0xFFFFF2BA, 0xFFFFF2B6,
	0xFFFFF2B3, 0xFFFFF2AF, 0xFFFFF2AC, 0xFFFFF2A8, 0xFFFFF2A5, 0xFFFFF2A1, 0xFFFFF29E, 0xFFFFF29B,
	0xFFFFF297, 0xFFFFF294, 0xFFFFF290, 0xFFFFF28D, 0xFFFFF289, 0xFFFFF286, 0xFFFFF283, 0xFFFFF27F,
	0xFFFFF27C, 0xFFFFF279, 0xFFFFF275, 0xFFFFF272, 0xFFFFF26F, 0xFFFFF26B, 0xFFFFF268, 0xFFFFF265,
	0xFFFFF261, 0xFFFFF25E, 0xFFFFF25B, 0xFFFFF257, 0xFFFFF254, 0xFFFFF251, 0xFFFFF24E, 0xFFFFF24A,
	0xFFFFF247, 0xFFFFF244, 0xFFFFF241, 0xFFFFF23E, 0xFFFFF23A, 0xFFFFF237, 0xFFFFF234, 0xFFFFF231,
	0xFFFFF22E, 0xFFFFF22B, 0xFFFFF227, 0xFFFFF224, 0xFFFFF221, 0xFFFFF21E, 0xFFFFF21B, 0xFFFFF218,
	0xFFFFF215, 0xFFFFF212, 0xFFFFF20E, 0xFFFFF20B, 0xFFFFF208, 0xFFFFF205, 0xFFFFF202, 0xFFFFF1FF,
	0xFFFFF1FC, 0xFFFFF1F9, 0xFFFFF1F6, 0xFFFFF1F3, 0xFFFFF1F0, 0xFFFFF1ED, 0xFFFFF1EA, 0xFFFFF1E7,
	0xFFFFF1E4, 0xFFFFF1E1, 0xFFFFF1DE, 0xFFFFF1DB, 0xFFFFF1D8, 0xFFFFF1D5, 0xFFFFF1D3, 0xFFFFF1D0,
	0xFFFFF1CD, 0xFFFFF1CA, 0xFFFFF1C7, 0xFFFFF1C4, 0xFFFFF1C1, 0xFFFFF1BE, 0xFFFFF1BC, 0xFFFFF1B9,
	0xFFFFF1B6, 0xFFFFF1B3, 0xFFFFF1B0, 0xFFFFF1AD, 0xFFFFF1AB, 0xFFFFF1A8, 0xFFFFF1A5, 0xFFFFF1A2,
	0xFFFFF1A0, 0xFFFFF19D, 0xFFFFF19A, 0xFFFFF197, 0xFFFFF195, 0xFFFFF192, 0xFFFFF18F, 0xFFFFF18C,
	0xFFFFF18A, 0xFFFFF187, 0xFFFFF184, 0xFFFFF182, 0xFFFFF17F, 0xFFFFF17C, 0xFFFFF17A, 0xFFFFF177,
	0xFFFFF175, 0xFFFFF172, 0xFFFFF16F, 0xFFFFF16D, 0xFFFFF16A, 0xFFFFF168, 0xFFFFF165, 0xFFFFF162,
	0xFFFFF160, 0xFFFFF15D, 0xFFFFF15B, 0xFFFFF158, 0xFFFFF156, 0xFFFFF153, 0xFFFFF151, 0xFFFFF14E,
	0xFFFFF14C, 0xFFFFF149, 0xFFFFF147, 0xFFFFF144, 0xFFFFF142, 0xFFFFF140, 0xFFFFF13D, 0xFFFFF13B,
	0xFFFFF138, 0xFFFFF136, 0xFFFFF133, 0xFFFFF131, 0xFFFFF12F, 0xFFFFF12C, 0xFFFFF12A, 0xFFFFF128,
	0xFFFFF125, 0xFFFFF123, 0xFFFFF121, 0xFFFFF11E, 0xFFFFF11C, 0xFFFFF11A, 0xFFFFF118, 0xFFFFF115,
	0xFFFFF113, 0xFFFFF111, 0xFFFFF10E, 0xFFFFF10C, 0xFFFFF10A, 0xFFFFF108, 0xFFFFF106, 0xFFFFF103,
	0xFFFFF101, 0xFFFFF0FF, 0xFFFFF0FD, 0xFFFFF0FB, 0xFFFFF0F8, 0xFFFFF0F6, 0xFFFFF0F4, 0xFFFFF0F2,
	0xFFFFF0F0, 0xFFFFF0EE, 0xFFFFF0EC, 0xFFFFF0EA, 0xFFFFF0E8, 0xFFFFF0E5, 0xFFFFF0E3, 0xFFFFF0E1,
	0xFFFFF0DF, 0xFFFFF0DD, 0xFFFFF0DB, 0xFFFFF0D9, 0xFFFFF0D7, 0xFFFFF0D5, 0xFFFFF0D3, 0xFFFFF0D1,
	0xFFFFF0CF, 0xFFFFF0CD, 0xFFFFF0CB, 0xFFFFF0C9, 0xFFFFF0C7, 0xFFFFF0C5, 0xFFFFF0C4, 0xFFFFF0C2,
	0xFFFFF0C0, 0xFFFFF0BE, 0xFFFFF0BC, 0xFFFFF0BA, 0xFFFFF0B8, 0xFFFFF0B6, 0xFFFFF0B5, 0xFFFFF0B3,
	0xFFFFF0B1, 0xFFFFF0AF, 0xFFFFF0AD, 0xFFFFF0AB, 0xFFFFF0AA, 0xFFFFF0A8, 0xFFFFF0A6, 0xFFFFF0A4,
	0xFFFFF0A3, 0xFFFFF0A1, 0xFFFFF09F, 0xFFFFF09D, 0xFFFFF09C, 0xFFFFF09A, 0xFFFFF098, 0xFFFFF097,
	0xFFFFF095, 0xFFFFF093, 0xFFFFF092, 0xFFFFF090, 0xFFFFF08E, 0xFFFFF08D, 0xFFFFF08B, 0xFFFFF089,
	0xFFFFF088, 0xFFFFF086, 0xFFFFF085, 0xFFFFF083, 0xFFFFF081, 0xFFFFF080, 0xFFFFF07E, 0xFFFFF07D,
	0xFFFFF07B, 0xFFFFF07A, 0xFFFFF078, 0xFFFFF077, 0xFFFFF075, 0xFFFFF074, 0xFFFFF072, 0xFFFFF071,
	0xFFFFF06F, 0xFFFFF06E, 0xFFFFF06C, 0xFFFFF06B, 0xFFFFF06A, 0xFFFFF068, 0xFFFFF067, 0xFFFFF065,
	0xFFFFF064, 0xFFFFF063, 0xFFFFF061, 0xFFFFF060, 0xFFFFF05F, 0xFFFFF05D, 0xFFFFF05C, 0xFFFFF05B,
	0xFFFFF059, 0xFFFFF058, 0xFFFFF057, 0xFFFFF055, 0xFFFFF054, 0xFFFFF053, 0xFFFFF052, 0xFFFFF050,
	0xFFFFF04F, 0xFFFFF04E, 0xFFFFF04D, 0xFFFFF04C, 0xFFFFF04A, 0xFFFFF049, 0xFFFFF048, 0xFFFFF047,
	0xFFFFF046, 0xFFFFF045, 0xFFFFF043, 0xFFFFF042, 0xFFFFF041, 0xFFFFF040, 0xFFFFF03F, 0xFFFFF03E,
	0xFFFFF03D, 0xFFFFF03C, 0xFFFFF03B, 0xFFFFF03A, 0xFFFFF039, 0xFFFFF038, 0xFFFFF037, 0xFFFFF036,
	0xFFFFF035, 0xFFFFF034, 0xFFFFF033, 0xFFFFF032, 0xFFFFF031, 0xFFFFF030, 0xFFFFF02F, 0xFFFFF02E,
	0xFFFFF02D, 0xFFFFF02C, 0xFFFFF02B, 0xFFFFF02A, 0xFFFFF029, 0xFFFFF028, 0xFFFFF027, 0xFFFFF027,
	0xFFFFF026, 0xFFFFF025, 0xFFFFF024, 0xFFFFF023, 0xFFFFF022, 0xFFFFF022, 0xFFFFF021, 0xFFFFF020,
	0xFFFFF01F, 0xFFFFF01F, 0xFFFFF01E, 0xFFFFF01D, 0xFFFFF01C, 0xFFFFF01C, 0xFFFFF01B, 0xFFFFF01A,
	0xFFFFF019, 0xFFFFF019, 0xFFFFF018, 0xFFFFF017, 0xFFFFF017, 0xFFFFF016, 0xFFFFF015, 0xFFFFF015,
	0xFFFFF014, 0xFFFFF014, 0xFFFFF013, 0xFFFFF012, 0xFFFFF012, 0xFFFFF011, 0xFFFFF011, 0xFFFFF010,
	0xFFFFF010, 0xFFFFF00F, 0xFFFFF00F, 0xFFFFF00E, 0xFFFFF00E, 0xFFFFF00D, 0xFFFFF00D, 0xFFFFF00C,
	0xFFFFF00C, 0xFFFFF00B, 0xFFFFF00B, 0xFFFFF00A, 0xFFFFF00A, 0xFFFFF009, 0xFFFFF009, 0xFFFFF009,
	0xFFFFF008, 0xFFFFF008, 0xFFFFF007, 0xFFFFF007, 0xFFFFF007, 0xFFFFF006, 0xFFFFF006, 0xFFFFF006,
	0xFFFFF005, 0xFFFFF005, 0xFFFFF005, 0xFFFFF005, 0xFFFFF004, 0xFFFFF004, 0xFFFFF004, 0xFFFFF004,
	0xFFFFF003, 0xFFFFF003, 0xFFFFF003, 0xFFFFF003, 0xFFFFF002, 0xFFFFF002, 0xFFFFF002, 0xFFFFF002,
	0xFFFFF002, 0xFFFFF002, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001,
	0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001,
	0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001,
	0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF001, 0xFFFFF002,
	0xFFFFF002, 0xFFFFF002, 0xFFFFF002, 0xFFFFF002, 0xFFFFF002, 0xFFFFF003, 0xFFFFF003, 0xFFFFF003,
	0xFFFFF003, 0xFFFFF004, 0xFFFFF004, 0xFFFFF004, 0xFFFFF004, 0xFFFFF005, 0xFFFFF005, 0xFFFFF005,
	0xFFFFF005, 0xFFFFF006, 0xFFFFF006, 0xFFFFF006, 0xFFFFF007, 0xFFFFF007, 0xFFFFF007, 0xFFFFF008,
	0xFFFFF008, 0xFFFFF009, 0xFFFFF009, 0xFFFFF009, 0xFFFFF00A, 0xFFFFF00A, 0xFFFFF00B, 0xFFFFF00B,
	0xFFFFF00C, 0xFFFFF00C, 0xFFFFF00D, 0xFFFFF00D, 0xFFFFF00E, 0xFFFFF00E, 0xFFFFF00F, 0xFFFFF00F,
	0xFFFFF010, 0xFFFFF010, 0xFFFFF011, 0xFFFFF011, 0xFFFFF012, 0xFFFFF012, 0xFFFFF013, 0xFFFFF014,
	0xFFFFF014, 0xFFFFF015, 0xFFFFF015, 0xFFFFF016, 0xFFFFF017, 0xFFFFF017, 0xFFFFF018, 0xFFFFF019,
	0xFFFFF019, 0xFFFFF01A, 0xFFFFF01B, 0xFFFFF01C, 0xFFFFF01C, 0xFFFFF01D, 0xFFFFF01E, 0xFFFFF01F,
	0xFFFFF01F, 0xFFFFF020, 0xFFFFF021, 0xFFFFF022, 0xFFFFF022, 0xFFFFF023, 0xFFFFF024, 0xFFFFF025,
	0xFFFFF026, 0xFFFFF027, 0xFFFFF027, 0xFFFFF028, 0xFFFFF029, 0xFFFFF02A, 0xFFFFF02B, 0xFFFFF02C,
	0xFFFFF02D, 0xFFFFF02E, 0xFFFFF02F, 0xFFFFF030, 0xFFFFF031, 0xFFFFF032, 0xFFFFF033, 0xFFFFF034,
	0xFFFFF035, 0xFFFFF036, 0xFFFFF037, 0xFFFFF038, 0xFFFFF039, 0xFFFFF03A, 0xFFFFF03B, 0xFFFFF03C,
	0xFFFFF03D, 0xFFFFF03E, 0xFFFFF03F, 0xFFFFF040, 0xFFFFF041, 0xFFFFF042, 0xFFFFF043, 0xFFFFF045,
	0xFFFFF046, 0xFFFFF047, 0xFFFFF048, 0xFFFFF049, 0xFFFFF04A, 0xFFFFF04C, 0xFFFFF04D, 0xFFFFF04E,
	0xFFFFF04F, 0xFFFFF050, 0xFFFFF052, 0xFFFFF053, 0xFFFFF054, 0xFFFFF055, 0xFFFFF057, 0xFFFFF058,
	0xFFFFF059, 0xFFFFF05B, 0xFFFFF05C, 0xFFFFF05D, 0xFFFFF05F, 0xFFFFF060, 0xFFFFF061, 0xFFFFF063,
	0xFFFFF064, 0xFFFFF065, 0xFFFFF067, 0xFFFFF068, 0xFFFFF06A, 0xFFFFF06B, 0xFFFFF06C, 0xFFFFF06E,
	0xFFFFF06F, 0xFFFFF071, 0xFFFFF072, 0xFFFFF074, 0xFFFFF075, 0xFFFFF077, 0xFFFFF078, 0xFFFFF07A,
	0xFFFFF07B, 0xFFFFF07D, 0xFFFFF07E, 0xFFFFF080, 0xFFFFF081, 0xFFFFF083, 0xFFFFF085, 0xFFFFF086,
	0xFFFFF088, 0xFFFFF089, 0xFFFFF08B, 0xFFFFF08D, 0xFFFFF08E, 0xFFFFF090, 0xFFFFF092, 0xFFFFF093,
	0xFFFFF095, 0xFFFFF097, 0xFFFFF098, 0xFFFFF09A, 0xFFFFF09C, 0xFFFFF09D, 0xFFFFF09F, 0xFFFFF0A1,
	0xFFFFF0A3, 0xFFFFF0A4, 0xFFFFF0A6, 0xFFFFF0A8, 0xFFFFF0AA, 0xFFFFF0AB, 0xFFFFF0AD, 0xFFFFF0AF,
	0xFFFFF0B1, 0xFFFFF0B3, 0xFFFFF0B5, 0xFFFFF0B6, 0xFFFFF0B8, 0xFFFFF0BA, 0xFFFFF0BC, 0xFFFFF0BE,
	0xFFFFF0C0, 0xFFFFF0C2, 0xFFFFF0C4, 0xFFFFF0C5, 0xFFFFF0C7, 0xFFFFF0C9, 0xFFFFF0CB, 0xFFFFF0CD,
	0xFFFFF0CF, 0xFFFFF0D1, 0xFFFFF0D3, 0xFFFFF0D5, 0xFFFFF0D7, 0xFFFFF0D9, 0xFFFFF0DB, 0xFFFFF0DD,
	0xFFFFF0DF, 0xFFFFF0E1, 0xFFFFF0E3, 0xFFFFF0E5, 0xFFFFF0E8, 0xFFFFF0EA, 0xFFFFF0EC, 0xFFFFF0EE,
	0xFFFFF0F0, 0xFFFFF0F2, 0xFFFFF0F4, 0xFFFFF0F6, 0xFFFFF0F8, 0xFFFFF0FB, 0xFFFFF0FD, 0xFFFFF0FF,
	0xFFFFF101, 0xFFFFF103, 0xFFFFF106, 0xFFFFF108, 0xFFFFF10A, 0xFFFFF10C, 0xFFFFF10E, 0xFFFFF111,
	0xFFFFF113, 0xFFFFF115, 0xFFFFF118, 0xFFFFF11A, 0xFFFFF11C, 0xFFFFF11E, 0xFFFFF121, 0xFFFFF123,
	0xFFFFF125, 0xFFFFF128, 0xFFFFF12A, 0xFFFFF12C, 0xFFFFF12F, 0xFFFFF131, 0xFFFFF133, 0xFFFFF136,
	0xFFFFF138, 0xFFFFF13B, 0xFFFFF13D, 0xFFFFF140, 0xFFFFF142, 0xFFFFF144, 0xFFFFF147, 0xFFFFF149,
	0xFFFFF14C, 0xFFFFF14E, 0xFFFFF151, 0xFFFFF153, 0xFFFFF156, 0xFFFFF158, 0xFFFFF15B, 0xFFFFF15D,
	0xFFFFF160, 0xFFFFF162, 0xFFFFF165, 0xFFFFF168, 0xFFFFF16A, 0xFFFFF16D, 0xFFFFF16F, 0xFFFFF172,
	0xFFFFF175, 0xFFFFF177, 0xFFFFF17A, 0xFFFFF17C, 0xFFFFF17F, 0xFFFFF182, 0xFFFFF184, 0xFFFFF187,
	0xFFFFF18A, 0xFFFFF18C, 0xFFFFF18F, 0xFFFFF192, 0xFFFFF195, 0xFFFFF197, 0xFFFFF19A, 0xFFFFF19D,
	0xFFFFF1A0, 0xFFFFF1A2, 0xFFFFF1A5, 0xFFFFF1A8, 0xFFFFF1AB, 0xFFFFF1AD, 0xFFFFF1B0, 0xFFFFF1B3,
	0xFFFFF1B6, 0xFFFFF1B9, 0xFFFFF1BC, 0xFFFFF1BE, 0xFFFFF1C1, 0xFFFFF1C4, 0xFFFFF1C7, 0xFFFFF1CA,
	0xFFFFF1CD, 0xFFFFF1D0, 0xFFFFF1D3, 0xFFFFF1D5, 0xFFFFF1D8, 0xFFFFF1DB, 0xFFFFF1DE, 0xFFFFF1E1,
	0xFFFFF1E4, 0xFFFFF1E7, 0xFFFFF1EA, 0xFFFFF1ED, 0xFFFFF1F0, 0xFFFFF1F3, 0xFFFFF1F6, 0xFFFFF1F9,
	0xFFFFF1FC, 0xFFFFF1FF, 0xFFFFF202, 0xFFFFF205, 0xFFFFF208, 0xFFFFF20B, 0xFFFFF20E, 0xFFFFF212,
	0xFFFFF215, 0xFFFFF218, 0xFFFFF21B, 0xFFFFF21E, 0xFFFFF221, 0xFFFFF224, 0xFFFFF227, 0xFFFFF22B,
	0xFFFFF22E, 0xFFFFF231, 0xFFFFF234, 0xFFFFF237, 0xFFFFF23A, 0xFFFFF23E, 0xFFFFF241, 0xFFFFF244,
	0xFFFFF247, 0xFFFFF24A, 0xFFFFF24E, 0xFFFFF251, 0xFFFFF254, 0xFFFFF257, 0xFFFFF25B, 0xFFFFF25E,
	0xFFFFF261, 0xFFFFF265, 0xFFFFF268, 0xFFFFF26B, 0xFFFFF26F, 0xFFFFF272, 0xFFFFF275, 0xFFFFF279,
	0xFFFFF27C, 0xFFFFF27F, 0xFFFFF283, 0xFFFFF286, 0xFFFFF289, 0xFFFFF28D, 0xFFFFF290, 0xFFFFF294,
	0xFFFFF297, 0xFFFFF29B, 0xFFFFF29E, 0xFFFFF2A1, 0xFFFFF2A5, 0xFFFFF2A8, 0xFFFFF2AC, 0xFFFFF2AF,
	0xFFFFF2B3, 0xFFFFF2B6, 0xFFFFF2BA, 0xFFFFF2BD, 0xFFFFF2C1, 0xFFFFF2C4, 0xFFFFF2C8, 0xFFFFF2CB,
	0xFFFFF2CF, 0xFFFFF2D3, 0xFFFFF2D6, 0xFFFFF2DA, 0xFFFFF2DD, 0xFFFFF2E1, 0xFFFFF2E4, 0xFFFFF2E8,
	0xFFFFF2EC, 0xFFFFF2EF, 0xFFFFF2F3, 0xFFFFF2F7, 0xFFFFF2FA, 0xFFFFF2FE, 0xFFFFF302, 0xFFFFF305,
	0xFFFFF309, 0xFFFFF30D, 0xFFFFF310, 0xFFFFF314, 0xFFFFF318, 0xFFFFF31B, 0xFFFFF31F, 0xFFFFF323,
	0xFFFFF327, 0xFFFFF32A, 0xFFFFF32E, 0xFFFFF332, 0xFFFFF336, 0xFFFFF339, 0xFFFFF33D, 0xFFFFF341,
	0xFFFFF345, 0xFFFFF349, 0xFFFFF34C, 0xFFFFF350, 0xFFFFF354, 0xFFFFF358, 0xFFFFF35C, 0xFFFFF360,
	0xFFFFF363, 0xFFFFF367, 0xFFFFF36B, 0xFFFFF36F, 0xFFFFF373, 0xFFFFF377, 0xFFFFF37B, 0xFFFFF37F,
	0xFFFFF383, 0xFFFFF387, 0xFFFFF38A, 0xFFFFF38E, 0xFFFFF392, 0xFFFFF396, 0xFFFFF39A, 0xFFFFF39E,
	0xFFFFF3A2, 0xFFFFF3A6, 0xFFFFF3AA, 0xFFFFF3AE, 0xFFFFF3B2, 0xFFFFF3B6, 0xFFFFF3BA, 0xFFFFF3BE,
	0xFFFFF3C2, 0xFFFFF3C6, 0xFFFFF3CA, 0xFFFFF3CF, 0xFFFFF3D3, 0xFFFFF3D7, 0xFFFFF3DB, 0xFFFFF3DF,
	0xFFFFF3E3, 0xFFFFF3E7, 0xFFFFF3EB, 0xFFFFF3EF, 0xFFFFF3F3, 0xFFFFF3F8, 0xFFFFF3FC, 0xFFFFF400,
	0xFFFFF404, 0xFFFFF408, 0xFFFFF40C, 0xFFFFF411, 0xFFFFF415, 0xFFFFF419, 0xFFFFF41D, 0xFFFFF421,
	0xFFFFF426, 0xFFFFF42A, 0xFFFFF42E, 0xFFFFF432, 0xFFFFF436, 0xFFFFF43B, 0xFFFFF43F, 0xFFFFF443,
	0xFFFFF448, 0xFFFFF44C, 0xFFFFF450, 0xFFFFF454, 0xFFFFF459, 0xFFFFF45D, 0xFFFFF461, 0xFFFFF466,
	0xFFFFF46A, 0xFFFFF46E, 0xFFFFF473, 0xFFFFF477, 0xFFFFF47B, 0xFFFFF480, 0xFFFFF484, 0xFFFFF488,
	0xFFFFF48D, 0xFFFFF491, 0xFFFFF496, 0xFFFFF49A, 0xFFFFF49E, 0xFFFFF4A3, 0xFFFFF4A7, 0xFFFFF4AC,
	0xFFFFF4B0, 0xFFFFF4B5, 0xFFFFF4B9, 0xFFFFF4BE, 0xFFFFF4C2, 0xFFFFF4C6, 0xFFFFF4CB, 0xFFFFF4CF,
	0xFFFFF4D4, 0xFFFFF4D8, 0xFFFFF4DD, 0xFFFFF4E1, 0xFFFFF4E6, 0xFFFFF4EB, 0xFFFFF4EF, 0xFFFFF4F4,
	0xFFFFF4F8, 0xFFFFF4FD, 0xFFFFF501, 0xFFFFF506, 0xFFFFF50A, 0xFFFFF50F, 0xFFFFF514, 0xFFFFF518,
	0xFFFFF51D, 0xFFFFF521, 0xFFFFF526, 0xFFFFF52B, 0xFFFFF52F, 0xFFFFF534, 0xFFFFF538, 0xFFFFF53D,
	0xFFFFF542, 0xFFFFF546, 0xFFFFF54B, 0xFFFFF550, 0xFFFFF554, 0xFFFFF559, 0xFFFFF55E, 0xFFFFF563,
	0xFFFFF567, 0xFFFFF56C, 0xFFFFF571, 0xFFFFF575, 0xFFFFF57A, 0xFFFFF57F, 0xFFFFF584, 0xFFFFF588,
	0xFFFFF58D, 0xFFFFF592, 0xFFFFF597, 0xFFFFF59B, 0xFFFFF5A0, 0xFFFFF5A5, 0xFFFFF5AA, 0xFFFFF5AF,
	0xFFFFF5B3, 0xFFFFF5B8, 0xFFFFF5BD, 0xFFFFF5C2, 0xFFFFF5C7, 0xFFFFF5CB, 0xFFFFF5D0, 0xFFFFF5D5,
	0xFFFFF5DA, 0xFFFFF5DF, 0xFFFFF5E4, 0xFFFFF5E9, 0xFFFFF5EE, 0xFFFFF5F2, 0xFFFFF5F7, 0xFFFFF5FC,
	0xFFFFF601, 0xFFFFF606, 0xFFFFF60B, 0xFFFFF610, 0xFFFFF615, 0xFFFFF61A, 0xFFFFF61F, 0xFFFFF624,
	0xFFFFF629, 0xFFFFF62D, 0xFFFFF632, 0xFFFFF637, 0xFFFFF63C, 0xFFFFF641, 0xFFFFF646, 0xFFFFF64B,
	0xFFFFF650, 0xFFFFF655, 0xFFFFF65A, 0xFFFFF65F, 0xFFFFF664, 0xFFFFF669, 0xFFFFF66E, 0xFFFFF673,
	0xFFFFF679, 0xFFFFF67E, 0xFFFFF683, 0xFFFFF688, 0xFFFFF68D, 0xFFFFF692, 0xFFFFF697, 0xFFFFF69C,
	0xFFFFF6A1, 0xFFFFF6A6, 0xFFFFF6AB, 0xFFFFF6B0, 0xFFFFF6B5, 0xFFFFF6BB, 0xFFFFF6C0, 0xFFFFF6C5,
	0xFFFFF6CA, 0xFFFFF6CF, 0xFFFFF6D4, 0xFFFFF6D9, 0xFFFFF6DF, 0xFFFFF6E4, 0xFFFFF6E9, 0xFFFFF6EE,
	0xFFFFF6F3, 0xFFFFF6F8, 0xFFFFF6FE, 0xFFFFF703, 0xFFFFF708, 0xFFFFF70D, 0xFFFFF712, 0xFFFFF718,
	0xFFFFF71D, 0xFFFFF722, 0xFFFFF727, 0xFFFFF72D, 0xFFFFF732, 0xFFFFF737, 0xFFFFF73C, 0xFFFFF742,
	0xFFFFF747, 0xFFFFF74C, 0xFFFFF751, 0xFFFFF757, 0xFFFFF75C, 0xFFFFF761, 0xFFFFF767, 0xFFFFF76C,
	0xFFFFF771, 0xFFFFF776, 0xFFFFF77C, 0xFFFFF781, 0xFFFFF786, 0xFFFFF78C, 0xFFFFF791, 0xFFFFF796,
	0xFFFFF79C, 0xFFFFF7A1, 0xFFFFF7A6, 0xFFFFF7AC, 0xFFFFF7B1, 0xFFFFF7B7, 0xFFFFF7BC, 0xFFFFF7C1,
	0xFFFFF7C7, 0xFFFFF7CC, 0xFFFFF7D2, 0xFFFFF7D7, 0xFFFFF7DC, 0xFFFFF7E2, 0xFFFFF7E7, 0xFFFFF7ED,
	0xFFFFF7F2, 0xFFFFF7F7, 0xFFFFF7FD, 0xFFFFF802, 0xFFFFF808, 0xFFFFF80D, 0xFFFFF813, 0xFFFFF818,
	0xFFFFF81E, 0xFFFFF823, 0xFFFFF829, 0xFFFFF82E, 0xFFFFF833, 0xFFFFF839, 0xFFFFF83E, 0xFFFFF844,
	0xFFFFF849, 0xFFFFF84F, 0xFFFFF854, 0xFFFFF85A, 0xFFFFF860, 0xFFFFF865, 0xFFFFF86B, 0xFFFFF870,
	0xFFFFF876, 0xFFFFF87B, 0xFFFFF881, 0xFFFFF886, 0xFFFFF88C, 0xFFFFF891, 0xFFFFF897, 0xFFFFF89D,
	0xFFFFF8A2, 0xFFFFF8A8, 0xFFFFF8AD, 0xFFFFF8B3, 0xFFFFF8B8, 0xFFFFF8BE, 0xFFFFF8C4, 0xFFFFF8C9,
	0xFFFFF8CF, 0xFFFFF8D5, 0xFFFFF8DA, 0xFFFFF8E0, 0xFFFFF8E5, 0xFFFFF8EB, 0xFFFFF8F1, 0xFFFFF8F6,
	0xFFFFF8FC, 0xFFFFF902, 0xFFFFF907, 0xFFFFF90D, 0xFFFFF913, 0xFFFFF918, 0xFFFFF91E, 0xFFFFF924,
	0xFFFFF929, 0xFFFFF92F, 0xFFFFF935, 0xFFFFF93A, 0xFFFFF940, 0xFFFFF946, 0xFFFFF94B, 0xFFFFF951,
	0xFFFFF957, 0xFFFFF95D, 0xFFFFF962, 0xFFFFF968, 0xFFFFF96E, 0xFFFFF973, 0xFFFFF979, 0xFFFFF97F,
	0xFFFFF985, 0xFFFFF98A, 0xFFFFF990, 0xFFFFF996, 0xFFFFF99C, 0xFFFFF9A1, 0xFFFFF9A7, 0xFFFFF9AD,
	0xFFFFF9B3, 0xFFFFF9B8, 0xFFFFF9BE, 0xFFFFF9C4, 0xFFFFF9CA, 0xFFFFF9D0, 0xFFFFF9D5, 0xFFFFF9DB,
	0xFFFFF9E1, 0xFFFFF9E7, 0xFFFFF9ED, 0xFFFFF9F2, 0xFFFFF9F8, 0xFFFFF9FE, 0xFFFFFA04, 0xFFFFFA0A,
	0xFFFFFA10, 0xFFFFFA15, 0xFFFFFA1B, 0xFFFFFA21, 0xFFFFFA27, 0xFFFFFA2D, 0xFFFFFA33, 0xFFFFFA39,
	0xFFFFFA3E, 0xFFFFFA44, 0xFFFFFA4A, 0xFFFFFA50, 0xFFFFFA56, 0xFFFFFA5C, 0xFFFFFA62, 0xFFFFFA67,
	0xFFFFFA6D, 0xFFFFFA73, 0xFFFFFA79, 0xFFFFFA7F, 0xFFFFFA85, 0xFFFFFA8B, 0xFFFFFA91, 0xFFFFFA97,
	0xFFFFFA9D, 0xFFFFFAA3, 0xFFFFFAA8, 0xFFFFFAAE, 0xFFFFFAB4, 0xFFFFFABA, 0xFFFFFAC0, 0xFFFFFAC6,
	0xFFFFFACC, 0xFFFFFAD2, 0xFFFFFAD8, 0xFFFFFADE, 0xFFFFFAE4, 0xFFFFFAEA, 0xFFFFFAF0, 0xFFFFFAF6,
	0xFFFFFAFC, 0xFFFFFB02, 0xFFFFFB08, 0xFFFFFB0E, 0xFFFFFB14, 0xFFFFFB1A, 0xFFFFFB20, 0xFFFFFB25,
	0xFFFFFB2B, 0xFFFFFB31, 0xFFFFFB37, 0xFFFFFB3D, 0xFFFFFB43, 0xFFFFFB49, 0xFFFFFB4F, 0xFFFFFB55,
	0xFFFFFB5B, 0xFFFFFB62, 0xFFFFFB68, 0xFFFFFB6E, 0xFFFFFB74, 0xFFFFFB7A, 0xFFFFFB80, 0xFFFFFB86,
	0xFFFFFB8C, 0xFFFFFB92, 0xFFFFFB98, 0xFFFFFB9E, 0xFFFFFBA4, 0xFFFFFBAA, 0xFFFFFBB0, 0xFFFFFBB6,
	0xFFFFFBBC, 0xFFFFFBC2, 0xFFFFFBC8, 0xFFFFFBCE, 0xFFFFFBD4, 0xFFFFFBDA, 0xFFFFFBE0, 0xFFFFFBE6,
	0xFFFFFBED, 0xFFFFFBF3, 0xFFFFFBF9, 0xFFFFFBFF, 0xFFFFFC05, 0xFFFFFC0B, 0xFFFFFC11, 0xFFFFFC17,
	0xFFFFFC1D, 0xFFFFFC23, 0xFFFFFC29, 0xFFFFFC30, 0xFFFFFC36, 0xFFFFFC3C, 0xFFFFFC42, 0xFFFFFC48,
	0xFFFFFC4E, 0xFFFFFC54, 0xFFFFFC5A, 0xFFFFFC60, 0xFFFFFC67, 0xFFFFFC6D, 0xFFFFFC73, 0xFFFFFC79,
	0xFFFFFC7F, 0xFFFFFC85, 0xFFFFFC8B, 0xFFFFFC91, 0xFFFFFC98, 0xFFFFFC9E, 0xFFFFFCA4, 0xFFFFFCAA,
	0xFFFFFCB0, 0xFFFFFCB6, 0xFFFFFCBC, 0xFFFFFCC3, 0xFFFFFCC9, 0xFFFFFCCF, 0xFFFFFCD5, 0xFFFFFCDB,
	0xFFFFFCE1, 0xFFFFFCE8, 0xFFFFFCEE, 0xFFFFFCF4, 0xFFFFFCFA, 0xFFFFFD00, 0xFFFFFD06, 0xFFFFFD0D,
	0xFFFFFD13, 0xFFFFFD19, 0xFFFFFD1F, 0xFFFFFD25, 0xFFFFFD2B, 0xFFFFFD32, 0xFFFFFD38, 0xFFFFFD3E,
	0xFFFFFD44, 0xFFFFFD4A, 0xFFFFFD51, 0xFFFFFD57, 0xFFFFFD5D, 0xFFFFFD63, 0xFFFFFD69, 0xFFFFFD70,
	0xFFFFFD76, 0xFFFFFD7C, 0xFFFFFD82, 0xFFFFFD88, 0xFFFFFD8F, 0xFFFFFD95, 0xFFFFFD9B, 0xFFFFFDA1,
	0xFFFFFDA7, 0xFFFFFDAE, 0xFFFFFDB4, 0xFFFFFDBA, 0xFFFFFDC0, 0xFFFFFDC7, 0xFFFFFDCD, 0xFFFFFDD3,
	0xFFFFFDD9, 0xFFFFFDDF, 0xFFFFFDE6, 0xFFFFFDEC, 0xFFFFFDF2, 0xFFFFFDF8, 0xFFFFFDFF, 0xFFFFFE05,
	0xFFFFFE0B, 0xFFFFFE11, 0xFFFFFE18, 0xFFFFFE1E, 0xFFFFFE24, 0xFFFFFE2A, 0xFFFFFE31, 0xFFFFFE37,
	0xFFFFFE3D, 0xFFFFFE43, 0xFFFFFE4A, 0xFFFFFE50, 0xFFFFFE56, 0xFFFFFE5C, 0xFFFFFE63, 0xFFFFFE69,
	0xFFFFFE6F, 0xFFFFFE75, 0xFFFFFE7C, 0xFFFFFE82, 0xFFFFFE88, 0xFFFFFE8E, 0xFFFFFE95, 0xFFFFFE9B,
	0xFFFFFEA1, 0xFFFFFEA7, 0xFFFFFEAE, 0xFFFFFEB4, 0xFFFFFEBA, 0xFFFFFEC0, 0xFFFFFEC7, 0xFFFFFECD,
	0xFFFFFED3, 0xFFFFFED9, 0xFFFFFEE0, 0xFFFFFEE6, 0xFFFFFEEC, 0xFFFFFEF3, 0xFFFFFEF9, 0xFFFFFEFF,
	0xFFFFFF05, 0xFFFFFF0C, 0xFFFFFF12, 0xFFFFFF18, 0xFFFFFF1E, 0xFFFFFF25, 0xFFFFFF2B, 0xFFFFFF31,
	0xFFFFFF38, 0xFFFFFF3E, 0xFFFFFF44, 0xFFFFFF4A, 0xFFFFFF51, 0xFFFFFF57, 0xFFFFFF5D, 0xFFFFFF63,
	0xFFFFFF6A, 0xFFFFFF70, 0xFFFFFF76, 0xFFFFFF7D, 0xFFFFFF83, 0xFFFFFF89, 0xFFFFFF8F, 0xFFFFFF96,
	0xFFFFFF9C, 0xFFFFFFA2, 0xFFFFFFA9, 0xFFFFFFAF, 0xFFFFFFB5, 0xFFFFFFBB, 0xFFFFFFC2, 0xFFFFFFC8,
	0xFFFFFFCE, 0xFFFFFFD5, 0xFFFFFFDB, 0xFFFFFFE1, 0xFFFFFFE7, 0xFFFFFFEE, 0xFFFFFFF4, 0xFFFFFFFA
};

static Int arcCosLookup[2 * INT_ONE] = {
	0x00003243, 0x000031E9, 0x000031C3, 0x000031A7, 0x0000318E, 0x00003179, 0x00003166, 0x00003154,
	0x00003143, 0x00003134, 0x00003125, 0x00003117, 0x0000310A, 0x000030FD, 0x000030F1, 0x000030E5,
	0x000030D9, 0x000030CE, 0x000030C3, 0x000030B9, 0x000030AF, 0x000030A5, 0x0000309B, 0x00003091,
	0x00003088, 0x0000307F, 0x00003076, 0x0000306D, 0x00003064, 0x0000305C, 0x00003053, 0x0000304B,
	0x00003043, 0x0000303B, 0x00003033, 0x0000302C, 0x00003024, 0x0000301D, 0x00003015, 0x0000300E,
	0x00003007, 0x00002FFF, 0x00002FF8, 0x00002FF1, 0x00002FEB, 0x00002FE4, 0x00002FDD, 0x00002FD6,
	0x00002FD0, 0x00002FC9, 0x00002FC3, 0x00002FBC, 0x00002FB6, 0x00002FB0, 0x00002FAA, 0x00002FA4,
	0x00002F9D, 0x00002F97, 0x00002F91, 0x00002F8B, 0x00002F86, 0x00002F80, 0x00002F7A, 0x00002F74,
	0x00002F6E, 0x00002F69, 0x00002F63, 0x00002F5E, 0x00002F58, 0x00002F53, 0x00002F4D, 0x00002F48,
	0x00002F42, 0x00002F3D, 0x00002F38, 0x00002F32, 0x00002F2D, 0x00002F28, 0x00002F23, 0x00002F1E,
	0x00002F19, 0x00002F14, 0x00002F0F, 0x00002F0A, 0x00002F05, 0x00002F00, 0x00002EFB, 0x00002EF6,
	0x00002EF1, 0x00002EEC, 0x00002EE7, 0x00002EE2, 0x00002EDE, 0x00002ED9, 0x00002ED4, 0x00002ED0,
	0x00002ECB, 0x00002EC6, 0x00002EC2, 0x00002EBD, 0x00002EB9, 0x00002EB4, 0x00002EAF, 0x00002EAB,
	0x00002EA7, 0x00002EA2, 0x00002E9E, 0x00002E99, 0x00002E95, 0x00002E90, 0x00002E8C, 0x00002E88,
	0x00002E83, 0x00002E7F, 0x00002E7B, 0x00002E77, 0x00002E72, 0x00002E6E, 0x00002E6A, 0x00002E66,
	0x00002E62, 0x00002E5D, 0x00002E59, 0x00002E55, 0x00002E51, 0x00002E4D, 0x00002E49, 0x00002E45,
	0x00002E41, 0x00002E3D, 0x00002E39, 0x00002E35, 0x00002E31, 0x00002E2D, 0x00002E29, 0x00002E25,
	0x00002E21, 0x00002E1D, 0x00002E19, 0x00002E15, 0x00002E11, 0x00002E0E, 0x00002E0A, 0x00002E06,
	0x00002E02, 0x00002DFE, 0x00002DFB, 0x00002DF7, 0x00002DF3, 0x00002DEF, 0x00002DEC, 0x00002DE8,
	0x00002DE4, 0x00002DE0, 0x00002DDD, 0x00002DD9, 0x00002DD5, 0x00002DD2, 0x00002DCE, 0x00002DCA,
	0x00002DC7, 0x00002DC3, 0x00002DC0, 0x00002DBC, 0x00002DB9, 0x00002DB5, 0x00002DB1, 0x00002DAE,
	0x00002DAA, 0x00002DA7, 0x00002DA3, 0x00002DA0, 0x00002D9C, 0x00002D99, 0x00002D95, 0x00002D92,
	0x00002D8E, 0x00002D8B, 0x00002D88, 0x00002D84, 0x00002D81, 0x00002D7D, 0x00002D7A, 0x00002D76,
	0x00002D73, 0x00002D70, 0x00002D6C, 0x00002D69, 0x00002D66, 0x00002D62, 0x00002D5F, 0x00002D5C,
	0x00002D58, 0x00002D55, 0x00002D52, 0x00002D4F, 0x00002D4B, 0x00002D48, 0x00002D45, 0x00002D41,
	0x00002D3E, 0x00002D3B, 0x00002D38, 0x00002D35, 0x00002D31, 0x00002D2E, 0x00002D2B, 0x00002D28,
	0x00002D25, 0x00002D21, 0x00002D1E, 0x00002D1B, 0x00002D18, 0x00002D15, 0x00002D12, 0x00002D0E,
	0x00002D0B, 0x00002D08, 0x00002D05, 0x00002D02, 0x00002CFF, 0x00002CFC, 0x00002CF9, 0x00002CF6,
	0x00002CF3, 0x00002CF0, 0x00002CED, 0x00002CE9, 0x00002CE6, 0x00002CE3, 0x00002CE0, 0x00002CDD,
	0x00002CDA, 0x00002CD7, 0x00002CD4, 0x00002CD1, 0x00002CCE, 0x00002CCB, 0x00002CC8, 0x00002CC5,
	0x00002CC2, 0x00002CBF, 0x00002CBC, 0x00002CBA, 0x00002CB7, 0x00002CB4, 0x00002CB1, 0x00002CAE,
	0x00002CAB, 0x00002CA8, 0x00002CA5, 0x00002CA2, 0x00002C9F, 0x00002C9C, 0x00002C99, 0x00002C97,
	0x00002C94, 0x00002C91, 0x00002C8E, 0x00002C8B, 0x00002C88, 0x00002C85, 0x00002C83, 0x00002C80,
	0x00002C7D, 0x00002C7A, 0x00002C77, 0x00002C74, 0x00002C72, 0x00002C6F, 0x00002C6C, 0x00002C69,
	0x00002C66, 0x00002C64, 0x00002C61, 0x00002C5E, 0x00002C5B, 0x00002C58, 0x00002C56, 0x00002C53,
	0x00002C50, 0x00002C4D, 0x00002C4B, 0x00002C48, 0x00002C45, 0x00002C43, 0x00002C40, 0x00002C3D,
	0x00002C3A, 0x00002C38, 0x00002C35, 0x00002C32, 0x00002C30, 0x00002C2D, 0x00002C2A, 0x00002C27,
	0x00002C25, 0x00002C22, 0x00002C1F, 0x00002C1D, 0x00002C1A, 0x00002C17, 0x00002C15, 0x00002C12,
	0x00002C0F, 0x00002C0D, 0x00002C0A, 0x00002C08, 0x00002C05, 0x00002C02, 0x00002C00, 0x00002BFD,
	0x00002BFA, 0x00002BF8, 0x00002BF5, 0x00002BF3, 0x00002BF0, 0x00002BED, 0x00002BEB, 0x00002BE8,
	0x00002BE6, 0x00002BE3, 0x00002BE1, 0x00002BDE, 0x00002BDB, 0x00002BD9, 0x00002BD6, 0x00002BD4,
	0x00002BD1, 0x00002BCF, 0x00002BCC, 0x00002BCA, 0x00002BC7, 0x00002BC4, 0x00002BC2, 0x00002BBF,
	0x00002BBD, 0x00002BBA, 0x00002BB8, 0x00002BB5, 0x00002BB3, 0x00002BB0, 0x00002BAE, 0x00002BAB,
	0x00002BA9, 0x00002BA6, 0x00002BA4, 0x00002BA1, 0x00002B9F, 0x00002B9C, 0x00002B9A, 0x00002B97,
	0x00002B95, 0x00002B93, 0x00002B90, 0x00002B8E, 0x00002B8B, 0x00002B89, 0x00002B86, 0x00002B84,
	0x00002B81, 0x00002B7F, 0x00002B7C, 0x00002B7A, 0x00002B78, 0x00002B75, 0x00002B73, 0x00002B70,
	0x00002B6E, 0x00002B6C, 0x00002B69, 0x00002B67, 0x00002B64, 0x00002B62, 0x00002B60, 0x00002B5D,
	0x00002B5B, 0x00002B58, 0x00002B56, 0x00002B54, 0x00002B51, 0x00002B4F, 0x00002B4C, 0x00002B4A,
	0x00002B48, 0x00002B45, 0x00002B43, 0x00002B41, 0x00002B3E, 0x00002B3C, 0x00002B3A, 0x00002B37,
	0x00002B35, 0x00002B33, 0x00002B30, 0x00002B2E, 0x00002B2C, 0x00002B29, 0x00002B27, 0x00002B25,
	0x00002B22, 0x00002B20, 0x00002B1E, 0x00002B1B, 0x00002B19, 0x00002B17, 0x00002B14, 0x00002B12,
	0x00002B10, 0x00002B0D, 0x00002B0B, 0x00002B09, 0x00002B07, 0x00002B04, 0x00002B02, 0x00002B00,
	0x00002AFD, 0x00002AFB, 0x00002AF9, 0x00002AF7, 0x00002AF4, 0x00002AF2, 0x00002AF0, 0x00002AEE,
	0x00002AEB, 0x00002AE9, 0x00002AE7, 0x00002AE5, 0x00002AE2, 0x00002AE0, 0x00002ADE, 0x00002ADC,
	0x00002AD9, 0x00002AD7, 0x00002AD5, 0x00002AD3, 0x00002AD0, 0x00002ACE, 0x00002ACC, 0x00002ACA,
	0x00002AC8, 0x00002AC5, 0x00002AC3, 0x00002AC1, 0x00002ABF, 0x00002ABC, 0x00002ABA, 0x00002AB8,
	0x00002AB6, 0x00002AB4, 0x00002AB1, 0x00002AAF, 0x00002AAD, 0x00002AAB, 0x00002AA9, 0x00002AA7,
	0x00002AA4, 0x00002AA2, 0x00002AA0, 0x00002A9E, 0x00002A9C, 0x00002A99, 0x00002A97, 0x00002A95,
	0x00002A93, 0x00002A91, 0x00002A8F, 0x00002A8C, 0x00002A8A, 0x00002A88, 0x00002A86, 0x00002A84,
	0x00002A82, 0x00002A80, 0x00002A7D, 0x00002A7B, 0x00002A79, 0x00002A77, 0x00002A75, 0x00002A73,
	0x00002A71, 0x00002A6F, 0x00002A6C, 0x00002A6A, 0x00002A68, 0x00002A66, 0x00002A64, 0x00002A62,
	0x00002A60, 0x00002A5E, 0x00002A5B, 0x00002A59, 0x00002A57, 0x00002A55, 0x00002A53, 0x00002A51,
	0x00002A4F, 0x00002A4D, 0x00002A4B, 0x00002A49, 0x00002A46, 0x00002A44, 0x00002A42, 0x00002A40,
	0x00002A3E, 0x00002A3C, 0x00002A3A, 0x00002A38, 0x00002A36, 0x00002A34, 0x00002A32, 0x00002A30,
	0x00002A2E, 0x00002A2B, 0x00002A29, 0x00002A27, 0x00002A25, 0x00002A23, 0x00002A21, 0x00002A1F,
	0x00002A1D, 0x00002A1B, 0x00002A19, 0x00002A17, 0x00002A15, 0x00002A13, 0x00002A11, 0x00002A0F,
	0x00002A0D, 0x00002A0B, 0x00002A09, 0x00002A07, 0x00002A05, 0x00002A03, 0x00002A01, 0x000029FF,
	0x000029FC, 0x000029FA, 0x000029F8, 0x000029F6, 0x000029F4, 0x000029F2, 0x000029F0, 0x000029EE,
	0x000029EC, 0x000029EA, 0x000029E8, 0x000029E6, 0x000029E4, 0x000029E2, 0x000029E0, 0x000029DE,
	0x000029DC, 0x000029DA, 0x000029D8, 0x000029D6, 0x000029D4, 0x000029D2, 0x000029D0, 0x000029CE,
	0x000029CC, 0x000029CA, 0x000029C8, 0x000029C7, 0x000029C5, 0x000029C3, 0x000029C1, 0x000029BF,
	0x000029BD, 0x000029BB, 0x000029B9, 0x000029B7, 0x000029B5, 0x000029B3, 0x000029B1, 0x000029AF,
	0x000029AD, 0x000029AB, 0x000029A9, 0x000029A7, 0x000029A5, 0x000029A3, 0x000029A1, 0x0000299F,
	0x0000299D, 0x0000299B, 0x00002999, 0x00002998, 0x00002996, 0x00002994, 0x00002992, 0x00002990,
	0x0000298E, 0x0000298C, 0x0000298A, 0x00002988, 0x00002986, 0x00002984, 0x00002982, 0x00002980,
	0x0000297E, 0x0000297D, 0x0000297B, 0x00002979, 0x00002977, 0x00002975, 0x00002973, 0x00002971,
	0x0000296F, 0x0000296D, 0x0000296B, 0x00002969, 0x00002968, 0x00002966, 0x00002964, 0x00002962,
	0x00002960, 0x0000295E, 0x0000295C, 0x0000295A, 0x00002958, 0x00002956, 0x00002955, 0x00002953,
	0x00002951, 0x0000294F, 0x0000294D, 0x0000294B, 0x00002949, 0x00002947, 0x00002946, 0x00002944,
	0x00002942, 0x00002940, 0x0000293E, 0x0000293C, 0x0000293A, 0x00002938, 0x00002937, 0x00002935,
	0x00002933, 0x00002931, 0x0000292F, 0x0000292D, 0x0000292B, 0x0000292A, 0x00002928, 0x00002926,
	0x00002924, 0x00002922, 0x00002920, 0x0000291E, 0x0000291D, 0x0000291B, 0x00002919, 0x00002917,
	0x00002915, 0x00002913, 0x00002912, 0x00002910, 0x0000290E, 0x0000290C, 0x0000290A, 0x00002908,
	0x00002907, 0x00002905, 0x00002903, 0x00002901, 0x000028FF, 0x000028FD, 0x000028FC, 0x000028FA,
	0x000028F8, 0x000028F6, 0x000028F4, 0x000028F2, 0x000028F1, 0x000028EF, 0x000028ED, 0x000028EB,
	0x000028E9, 0x000028E8, 0x000028E6, 0x000028E4, 0x000028E2, 0x000028E0, 0x000028DF, 0x000028DD,
	0x000028DB, 0x000028D9, 0x000028D7, 0x000028D5, 0x000028D4, 0x000028D2, 0x000028D0, 0x000028CE,
	0x000028CD, 0x000028CB, 0x000028C9, 0x000028C7, 0x000028C5, 0x000028C4, 0x000028C2, 0x000028C0,
	0x000028BE, 0x000028BC, 0x000028BB, 0x000028B9, 0x000028B7, 0x000028B5, 0x000028B4, 0x000028B2,
	0x000028B0, 0x000028AE, 0x000028AC, 0x000028AB, 0x000028A9, 0x000028A7, 0x000028A5, 0x000028A4,
	0x000028A2, 0x000028A0, 0x0000289E, 0x0000289D, 0x0000289B, 0x00002899, 0x00002897, 0x00002895,
	0x00002894, 0x00002892, 0x00002890, 0x0000288E, 0x0000288D, 0x0000288B, 0x00002889, 0x00002887,
	0x00002886, 0x00002884, 0x00002882, 0x00002880, 0x0000287F, 0x0000287D, 0x0000287B, 0x00002879,
	0x00002878, 0x00002876, 0x00002874, 0x00002873, 0x00002871, 0x0000286F, 0x0000286D, 0x0000286C,
	0x0000286A, 0x00002868, 0x00002866, 0x00002865, 0x00002863, 0x00002861, 0x0000285F, 0x0000285E,
	0x0000285C, 0x0000285A, 0x00002859, 0x00002857, 0x00002855, 0x00002853, 0x00002852, 0x00002850,
	0x0000284E, 0x0000284D, 0x0000284B, 0x00002849, 0x00002847, 0x00002846, 0x00002844, 0x00002842,
	0x00002841, 0x0000283F, 0x0000283D, 0x0000283B, 0x0000283A, 0x00002838, 0x00002836, 0x00002835,
	0x00002833, 0x00002831, 0x00002830, 0x0000282E, 0x0000282C, 0x0000282A, 0x00002829, 0x00002827,
	0x00002825, 0x00002824, 0x00002822, 0x00002820, 0x0000281F, 0x0000281D, 0x0000281B, 0x0000281A,
	0x00002818, 0x00002816, 0x00002815, 0x00002813, 0x00002811, 0x0000280F, 0x0000280E, 0x0000280C,
	0x0000280A, 0x00002809, 0x00002807, 0x00002805, 0x00002804, 0x00002802, 0x00002800, 0x000027FF,
	0x000027FD, 0x000027FB, 0x000027FA, 0x000027F8, 0x000027F6, 0x000027F5, 0x000027F3, 0x000027F1,
	0x000027F0, 0x000027EE, 0x000027EC, 0x000027EB, 0x000027E9, 0x000027E7, 0x000027E6, 0x000027E4,
	0x000027E2, 0x000027E1, 0x000027DF, 0x000027DD, 0x000027DC, 0x000027DA, 0x000027D9, 0x000027D7,
	0x000027D5, 0x000027D4, 0x000027D2, 0x000027D0, 0x000027CF, 0x000027CD, 0x000027CB, 0x000027CA,
	0x000027C8, 0x000027C6, 0x000027C5, 0x000027C3, 0x000027C2, 0x000027C0, 0x000027BE, 0x000027BD,
	0x000027BB, 0x000027B9, 0x000027B8, 0x000027B6, 0x000027B4, 0x000027B3, 0x000027B1, 0x000027B0,
	0x000027AE, 0x000027AC, 0x000027AB, 0x000027A9, 0x000027A7, 0x000027A6, 0x000027A4, 0x000027A3,
	0x000027A1, 0x0000279F, 0x0000279E, 0x0000279C, 0x0000279A, 0x00002799, 0x00002797, 0x00002796,
	0x00002794, 0x00002792, 0x00002791, 0x0000278F, 0x0000278E, 0x0000278C, 0x0000278A, 0x00002789,
	0x00002787, 0x00002785, 0x00002784, 0x00002782, 0x00002781, 0x0000277F, 0x0000277D, 0x0000277C,
	0x0000277A, 0x00002779, 0x00002777, 0x00002775, 0x00002774, 0x00002772, 0x00002771, 0x0000276F,
	0x0000276D, 0x0000276C, 0x0000276A, 0x00002769, 0x00002767, 0x00002765, 0x00002764, 0x00002762,
	0x00002761, 0x0000275F, 0x0000275E, 0x0000275C, 0x0000275A, 0x00002759, 0x00002757, 0x00002756,
	0x00002754, 0x00002752, 0x00002751, 0x0000274F, 0x0000274E, 0x0000274C, 0x0000274B, 0x00002749,
	0x00002747, 0x00002746, 0x00002744, 0x00002743, 0x00002741, 0x00002740, 0x0000273E, 0x0000273C,
	0x0000273B, 0x00002739, 0x00002738, 0x00002736, 0x00002735, 0x00002733, 0x00002731, 0x00002730,
	0x0000272E, 0x0000272D, 0x0000272B, 0x0000272A, 0x00002728, 0x00002726, 0x00002725, 0x00002723,
	0x00002722, 0x00002720, 0x0000271F, 0x0000271D, 0x0000271B, 0x0000271A, 0x00002718, 0x00002717,
	0x00002715, 0x00002714, 0x00002712, 0x00002711, 0x0000270F, 0x0000270E, 0x0000270C, 0x0000270A,
	0x00002709, 0x00002707, 0x00002706, 0x00002704, 0x00002703, 0x00002701, 0x00002700, 0x000026FE,
	0x000026FC, 0x000026FB, 0x000026F9, 0x000026F8, 0x000026F6, 0x000026F5, 0x000026F3, 0x000026F2,
	0x000026F0, 0x000026EF, 0x000026ED, 0x000026EC, 0x000026EA, 0x000026E8, 0x000026E7, 0x000026E5,
	0x000026E4, 0x000026E2, 0x000026E1, 0x000026DF, 0x000026DE, 0x000026DC, 0x000026DB, 0x000026D9,
	0x000026D8, 0x000026D6, 0x000026D5, 0x000026D3, 0x000026D2, 0x000026D0, 0x000026CE, 0x000026CD,
	0x000026CB, 0x000026CA, 0x000026C8, 0x000026C7, 0x000026C5, 0x000026C4, 0x000026C2, 0x000026C1,
	0x000026BF, 0x000026BE, 0x000026BC, 0x000026BB, 0x000026B9, 0x000026B8, 0x000026B6, 0x000026B5,
	0x000026B3, 0x000026B2, 0x000026B0, 0x000026AF, 0x000026AD, 0x000026AC, 0x000026AA, 0x000026A9,
	0x000026A7, 0x000026A6, 0x000026A4, 0x000026A3, 0x000026A1, 0x000026A0, 0x0000269E, 0x0000269D,
	0x0000269B, 0x0000269A, 0x00002698, 0x00002697, 0x00002695, 0x00002694, 0x00002692, 0x00002691,
	0x0000268F, 0x0000268E, 0x0000268C, 0x0000268B, 0x00002689, 0x00002688, 0x00002686, 0x00002685,
	0x00002683, 0x00002682, 0x00002680, 0x0000267F, 0x0000267D, 0x0000267C, 0x0000267A, 0x00002679,
	0x00002677, 0x00002676, 0x00002674, 0x00002673, 0x00002671, 0x00002670, 0x0000266E, 0x0000266D,
	0x0000266B, 0x0000266A, 0x00002668, 0x00002667, 0x00002665, 0x00002664, 0x00002662, 0x00002661,
	0x0000265F, 0x0000265E, 0x0000265D, 0x0000265B, 0x0000265A, 0x00002658, 0x00002657, 0x00002655,
	0x00002654, 0x00002652, 0x00002651, 0x0000264F, 0x0000264E, 0x0000264C, 0x0000264B, 0x00002649,
	0x00002648, 0x00002646, 0x00002645, 0x00002643, 0x00002642, 0x00002641, 0x0000263F, 0x0000263E,
	0x0000263C, 0x0000263B, 0x00002639, 0x00002638, 0x00002636, 0x00002635, 0x00002633, 0x00002632,
	0x00002630, 0x0000262F, 0x0000262E, 0x0000262C, 0x0000262B, 0x00002629, 0x00002628, 0x00002626,
	0x00002625, 0x00002623, 0x00002622, 0x00002620, 0x0000261F, 0x0000261E, 0x0000261C, 0x0000261B,
	0x00002619, 0x00002618, 0x00002616, 0x00002615, 0x00002613, 0x00002612, 0x00002610, 0x0000260F,
	0x0000260E, 0x0000260C, 0x0000260B, 0x00002609, 0x00002608, 0x00002606, 0x00002605, 0x00002603,
	0x00002602, 0x00002601, 0x000025FF, 0x000025FE, 0x000025FC, 0x000025FB, 0x000025F9, 0x000025F8,
	0x000025F7, 0x000025F5, 0x000025F4, 0x000025F2, 0x000025F1, 0x000025EF, 0x000025EE, 0x000025EC,
	0x000025EB, 0x000025EA, 0x000025E8, 0x000025E7, 0x000025E5, 0x000025E4, 0x000025E2, 0x000025E1,
	0x000025E0, 0x000025DE, 0x000025DD, 0x000025DB, 0x000025DA, 0x000025D8, 0x000025D7, 0x000025D6,
	0x000025D4, 0x000025D3, 0x000025D1, 0x000025D0, 0x000025CE, 0x000025CD, 0x000025CC, 0x000025CA,
	0x000025C9, 0x000025C7, 0x000025C6, 0x000025C4, 0x000025C3, 0x000025C2, 0x000025C0, 0x000025BF,
	0x000025BD, 0x000025BC, 0x000025BB, 0x000025B9, 0x000025B8, 0x000025B6, 0x000025B5, 0x000025B3,
	0x000025B2, 0x000025B1, 0x000025AF, 0x000025AE, 0x000025AC, 0x000025AB, 0x000025AA, 0x000025A8,
	0x000025A7, 0x000025A5, 0x000025A4, 0x000025A3, 0x000025A1, 0x000025A0, 0x0000259E, 0x0000259D,
	0x0000259B, 0x0000259A, 0x00002599, 0x00002597, 0x00002596, 0x00002594, 0x00002593, 0x00002592,
	0x00002590, 0x0000258F, 0x0000258D, 0x0000258C, 0x0000258B, 0x00002589, 0x00002588, 0x00002586,
	0x00002585, 0x00002584, 0x00002582, 0x00002581, 0x0000257F, 0x0000257E, 0x0000257D, 0x0000257B,
	0x0000257A, 0x00002578, 0x00002577, 0x00002576, 0x00002574, 0x00002573, 0x00002572, 0x00002570,
	0x0000256F, 0x0000256D, 0x0000256C, 0x0000256B, 0x00002569, 0x00002568, 0x00002566, 0x00002565,
	0x00002564, 0x00002562, 0x00002561, 0x0000255F, 0x0000255E, 0x0000255D, 0x0000255B, 0x0000255A,
	0x00002559, 0x00002557, 0x00002556, 0x00002554, 0x00002553, 0x00002552, 0x00002550, 0x0000254F,
	0x0000254D, 0x0000254C, 0x0000254B, 0x00002549, 0x00002548, 0x00002547, 0x00002545, 0x00002544,
	0x00002542, 0x00002541, 0x00002540, 0x0000253E, 0x0000253D, 0x0000253C, 0x0000253A, 0x00002539,
	0x00002537, 0x00002536, 0x00002535, 0x00002533, 0x00002532, 0x00002531, 0x0000252F, 0x0000252E,
	0x0000252C, 0x0000252B, 0x0000252A, 0x00002528, 0x00002527, 0x00002526, 0x00002524, 0x00002523,
	0x00002522, 0x00002520, 0x0000251F, 0x0000251D, 0x0000251C, 0x0000251B, 0x00002519, 0x00002518,
	0x00002517, 0x00002515, 0x00002514, 0x00002513, 0x00002511, 0x00002510, 0x0000250E, 0x0000250D,
	0x0000250C, 0x0000250A, 0x00002509, 0x00002508, 0x00002506, 0x00002505, 0x00002504, 0x00002502,
	0x00002501, 0x000024FF, 0x000024FE, 0x000024FD, 0x000024FB, 0x000024FA, 0x000024F9, 0x000024F7,
	0x000024F6, 0x000024F5, 0x000024F3, 0x000024F2, 0x000024F1, 0x000024EF, 0x000024EE, 0x000024ED,
	0x000024EB, 0x000024EA, 0x000024E8, 0x000024E7, 0x000024E6, 0x000024E4, 0x000024E3, 0x000024E2,
	0x000024E0, 0x000024DF, 0x000024DE, 0x000024DC, 0x000024DB, 0x000024DA, 0x000024D8, 0x000024D7,
	0x000024D6, 0x000024D4, 0x000024D3, 0x000024D2, 0x000024D0, 0x000024CF, 0x000024CE, 0x000024CC,
	0x000024CB, 0x000024CA, 0x000024C8, 0x000024C7, 0x000024C6, 0x000024C4, 0x000024C3, 0x000024C2,
	0x000024C0, 0x000024BF, 0x000024BE, 0x000024BC, 0x000024BB, 0x000024BA, 0x000024B8, 0x000024B7,
	0x000024B5, 0x000024B4, 0x000024B3, 0x000024B1, 0x000024B0, 0x000024AF, 0x000024AD, 0x000024AC,
	0x000024AB, 0x000024AA, 0x000024A8, 0x000024A7, 0x000024A6, 0x000024A4, 0x000024A3, 0x000024A2,
	0x000024A0, 0x0000249F, 0x0000249E, 0x0000249C, 0x0000249B, 0x0000249A, 0x00002498, 0x00002497,
	0x00002496, 0x00002494, 0x00002493, 0x00002492, 0x00002490, 0x0000248F, 0x0000248E, 0x0000248C,
	0x0000248B, 0x0000248A, 0x00002488, 0x00002487, 0x00002486, 0x00002484, 0x00002483, 0x00002482,
	0x00002480, 0x0000247F, 0x0000247E, 0x0000247C, 0x0000247B, 0x0000247A, 0x00002479, 0x00002477,
	0x00002476, 0x00002475, 0x00002473, 0x00002472, 0x00002471, 0x0000246F, 0x0000246E, 0x0000246D,
	0x0000246B, 0x0000246A, 0x00002469, 0x00002467, 0x00002466, 0x00002465, 0x00002463, 0x00002462,
	0x00002461, 0x00002460, 0x0000245E, 0x0000245D, 0x0000245C, 0x0000245A, 0x00002459, 0x00002458,
	0x00002456, 0x00002455, 0x00002454, 0x00002452, 0x00002451, 0x00002450, 0x0000244F, 0x0000244D,
	0x0000244C, 0x0000244B, 0x00002449, 0x00002448, 0x00002447, 0x00002445, 0x00002444, 0x00002443,
	0x00002442, 0x00002440, 0x0000243F, 0x0000243E, 0x0000243C, 0x0000243B, 0x0000243A, 0x00002438,
	0x00002437, 0x00002436, 0x00002434, 0x00002433, 0x00002432, 0x00002431, 0x0000242F, 0x0000242E,
	0x0000242D, 0x0000242B, 0x0000242A, 0x00002429, 0x00002428, 0x00002426, 0x00002425, 0x00002424,
	0x00002422, 0x00002421, 0x00002420, 0x0000241E, 0x0000241D, 0x0000241C, 0x0000241B, 0x00002419,
	0x00002418, 0x00002417, 0x00002415, 0x00002414, 0x00002413, 0x00002412, 0x00002410, 0x0000240F,
	0x0000240E, 0x0000240C, 0x0000240B, 0x0000240A, 0x00002409, 0x00002407, 0x00002406, 0x00002405,
	0x00002403, 0x00002402, 0x00002401, 0x00002400, 0x000023FE, 0x000023FD, 0x000023FC, 0x000023FA,
	0x000023F9, 0x000023F8, 0x000023F7, 0x000023F5, 0x000023F4, 0x000023F3, 0x000023F1, 0x000023F0,
	0x000023EF, 0x000023EE, 0x000023EC, 0x000023EB, 0x000023EA, 0x000023E8, 0x000023E7, 0x000023E6,
	0x000023E5, 0x000023E3, 0x000023E2, 0x000023E1, 0x000023DF, 0x000023DE, 0x000023DD, 0x000023DC,
	0x000023DA, 0x000023D9, 0x000023D8, 0x000023D7, 0x000023D5, 0x000023D4, 0x000023D3, 0x000023D1,
	0x000023D0, 0x000023CF, 0x000023CE, 0x000023CC, 0x000023CB, 0x000023CA, 0x000023C9, 0x000023C7,
	0x000023C6, 0x000023C5, 0x000023C3, 0x000023C2, 0x000023C1, 0x000023C0, 0x000023BE, 0x000023BD,
	0x000023BC, 0x000023BB, 0x000023B9, 0x000023B8, 0x000023B7, 0x000023B6, 0x000023B4, 0x000023B3,
	0x000023B2, 0x000023B0, 0x000023AF, 0x000023AE, 0x000023AD, 0x000023AB, 0x000023AA, 0x000023A9,
	0x000023A8, 0x000023A6, 0x000023A5, 0x000023A4, 0x000023A3, 0x000023A1, 0x000023A0, 0x0000239F,
	0x0000239E, 0x0000239C, 0x0000239B, 0x0000239A, 0x00002398, 0x00002397, 0x00002396, 0x00002395,
	0x00002393, 0x00002392, 0x00002391, 0x00002390, 0x0000238E, 0x0000238D, 0x0000238C, 0x0000238B,
	0x00002389, 0x00002388, 0x00002387, 0x00002386, 0x00002384, 0x00002383, 0x00002382, 0x00002381,
	0x0000237F, 0x0000237E, 0x0000237D, 0x0000237C, 0x0000237A, 0x00002379, 0x00002378, 0x00002377,
	0x00002375, 0x00002374, 0x00002373, 0x00002372, 0x00002370, 0x0000236F, 0x0000236E, 0x0000236D,
	0x0000236B, 0x0000236A, 0x00002369, 0x00002368, 0x00002366, 0x00002365, 0x00002364, 0x00002363,
	0x00002361, 0x00002360, 0x0000235F, 0x0000235E, 0x0000235C, 0x0000235B, 0x0000235A, 0x00002359,
	0x00002357, 0x00002356, 0x00002355, 0x00002354, 0x00002352, 0x00002351, 0x00002350, 0x0000234F,
	0x0000234D, 0x0000234C, 0x0000234B, 0x0000234A, 0x00002348, 0x00002347, 0x00002346, 0x00002345,
	0x00002343, 0x00002342, 0x00002341, 0x00002340, 0x0000233E, 0x0000233D, 0x0000233C, 0x0000233B,
	0x0000233A, 0x00002338, 0x00002337, 0x00002336, 0x00002335, 0x00002333, 0x00002332, 0x00002331,
	0x00002330, 0x0000232E, 0x0000232D, 0x0000232C, 0x0000232B, 0x00002329, 0x00002328, 0x00002327,
	0x00002326, 0x00002325, 0x00002323, 0x00002322, 0x00002321, 0x00002320, 0x0000231E, 0x0000231D,
	0x0000231C, 0x0000231B, 0x00002319, 0x00002318, 0x00002317, 0x00002316, 0x00002315, 0x00002313,
	0x00002312, 0x00002311, 0x00002310, 0x0000230E, 0x0000230D, 0x0000230C, 0x0000230B, 0x00002309,
	0x00002308, 0x00002307, 0x00002306, 0x00002305, 0x00002303, 0x00002302, 0x00002301, 0x00002300,
	0x000022FE, 0x000022FD, 0x000022FC, 0x000022FB, 0x000022F9, 0x000022F8, 0x000022F7, 0x000022F6,
	0x000022F5, 0x000022F3, 0x000022F2, 0x000022F1, 0x000022F0, 0x000022EE, 0x000022ED, 0x000022EC,
	0x000022EB, 0x000022EA, 0x000022E8, 0x000022E7, 0x000022E6, 0x000022E5, 0x000022E3, 0x000022E2,
	0x000022E1, 0x000022E0, 0x000022DF, 0x000022DD, 0x000022DC, 0x000022DB, 0x000022DA, 0x000022D9,
	0x000022D7, 0x000022D6, 0x000022D5, 0x000022D4, 0x000022D2, 0x000022D1, 0x000022D0, 0x000022CF,
	0x000022CE, 0x000022CC, 0x000022CB, 0x000022CA, 0x000022C9, 0x000022C8, 0x000022C6, 0x000022C5,
	0x000022C4, 0x000022C3, 0x000022C1, 0x000022C0, 0x000022BF, 0x000022BE, 0x000022BD, 0x000022BB,
	0x000022BA, 0x000022B9, 0x000022B8, 0x000022B7, 0x000022B5, 0x000022B4, 0x000022B3, 0x000022B2,
	0x000022B0, 0x000022AF, 0x000022AE, 0x000022AD, 0x000022AC, 0x000022AA, 0x000022A9, 0x000022A8,
	0x000022A7, 0x000022A6, 0x000022A4, 0x000022A3, 0x000022A2, 0x000022A1, 0x000022A0, 0x0000229E,
	0x0000229D, 0x0000229C, 0x0000229B, 0x0000229A, 0x00002298, 0x00002297, 0x00002296, 0x00002295,
	0x00002294, 0x00002292, 0x00002291, 0x00002290, 0x0000228F, 0x0000228E, 0x0000228C, 0x0000228B,
	0x0000228A, 0x00002289, 0x00002287, 0x00002286, 0x00002285, 0x00002284, 0x00002283, 0x00002281,
	0x00002280, 0x0000227F, 0x0000227E, 0x0000227D, 0x0000227B, 0x0000227A, 0x00002279, 0x00002278,
	0x00002277, 0x00002276, 0x00002274, 0x00002273, 0x00002272, 0x00002271, 0x00002270, 0x0000226E,
	0x0000226D, 0x0000226C, 0x0000226B, 0x0000226A, 0x00002268, 0x00002267, 0x00002266, 0x00002265,
	0x00002264, 0x00002262, 0x00002261, 0x00002260, 0x0000225F, 0x0000225E, 0x0000225C, 0x0000225B,
	0x0000225A, 0x00002259, 0x00002258, 0x00002256, 0x00002255, 0x00002254, 0x00002253, 0x00002252,
	0x00002250, 0x0000224F, 0x0000224E, 0x0000224D, 0x0000224C, 0x0000224B, 0x00002249, 0x00002248,
	0x00002247, 0x00002246, 0x00002245, 0x00002243, 0x00002242, 0x00002241, 0x00002240, 0x0000223F,
	0x0000223D, 0x0000223C, 0x0000223B, 0x0000223A, 0x00002239, 0x00002238, 0x00002236, 0x00002235,
	0x00002234, 0x00002233, 0x00002232, 0x00002230, 0x0000222F, 0x0000222E, 0x0000222D, 0x0000222C,
	0x0000222A, 0x00002229, 0x00002228, 0x00002227, 0x00002226, 0x00002225, 0x00002223, 0x00002222,
	0x00002221, 0x00002220, 0x0000221F, 0x0000221D, 0x0000221C, 0x0000221B, 0x0000221A, 0x00002219,
	0x00002218, 0x00002216, 0x00002215, 0x00002214, 0x00002213, 0x00002212, 0x00002210, 0x0000220F,
	0x0000220E, 0x0000220D, 0x0000220C, 0x0000220B, 0x00002209, 0x00002208, 0x00002207, 0x00002206,
	0x00002205, 0x00002204, 0x00002202, 0x00002201, 0x00002200, 0x000021FF, 0x000021FE, 0x000021FC,
	0x000021FB, 0x000021FA, 0x000021F9, 0x000021F8, 0x000021F7, 0x000021F5, 0x000021F4, 0x000021F3,
	0x000021F2, 0x000021F1, 0x000021F0, 0x000021EE, 0x000021ED, 0x000021EC, 0x000021EB, 0x000021EA,
	0x000021E9, 0x000021E7, 0x000021E6, 0x000021E5, 0x000021E4, 0x000021E3, 0x000021E2, 0x000021E0,
	0x000021DF, 0x000021DE, 0x000021DD, 0x000021DC, 0x000021DA, 0x000021D9, 0x000021D8, 0x000021D7,
	0x000021D6, 0x000021D5, 0x000021D3, 0x000021D2, 0x000021D1, 0x000021D0, 0x000021CF, 0x000021CE,
	0x000021CC, 0x000021CB, 0x000021CA, 0x000021C9, 0x000021C8, 0x000021C7, 0x000021C5, 0x000021C4,
	0x000021C3, 0x000021C2, 0x000021C1, 0x000021C0, 0x000021BE, 0x000021BD, 0x000021BC, 0x000021BB,
	0x000021BA, 0x000021B9, 0x000021B7, 0x000021B6, 0x000021B5, 0x000021B4, 0x000021B3, 0x000021B2,
	0x000021B1, 0x000021AF, 0x000021AE, 0x000021AD, 0x000021AC, 0x000021AB, 0x000021AA, 0x000021A8,
	0x000021A7, 0x000021A6, 0x000021A5, 0x000021A4, 0x000021A3, 0x000021A1, 0x000021A0, 0x0000219F,
	0x0000219E, 0x0000219D, 0x0000219C, 0x0000219A, 0x00002199, 0x00002198, 0x00002197, 0x00002196,
	0x00002195, 0x00002194, 0x00002192, 0x00002191, 0x00002190, 0x0000218F, 0x0000218E, 0x0000218D,
	0x0000218B, 0x0000218A, 0x00002189, 0x00002188, 0x00002187, 0x00002186, 0x00002184, 0x00002183,
	0x00002182, 0x00002181, 0x00002180, 0x0000217F, 0x0000217E, 0x0000217C, 0x0000217B, 0x0000217A,
	0x00002179, 0x00002178, 0x00002177, 0x00002175, 0x00002174, 0x00002173, 0x00002172, 0x00002171,
	0x00002170, 0x0000216F, 0x0000216D, 0x0000216C, 0x0000216B, 0x0000216A, 0x00002169, 0x00002168,
	0x00002167, 0x00002165, 0x00002164, 0x00002163, 0x00002162, 0x00002161, 0x00002160, 0x0000215E,
	0x0000215D, 0x0000215C, 0x0000215B, 0x0000215A, 0x00002159, 0x00002158, 0x00002156, 0x00002155,
	0x00002154, 0x00002153, 0x00002152, 0x00002151, 0x00002150, 0x0000214E, 0x0000214D, 0x0000214C,
	0x0000214B, 0x0000214A, 0x00002149, 0x00002148, 0x00002146, 0x00002145, 0x00002144, 0x00002143,
	0x00002142, 0x00002141, 0x00002140, 0x0000213E, 0x0000213D, 0x0000213C, 0x0000213B, 0x0000213A,
	0x00002139, 0x00002137, 0x00002136, 0x00002135, 0x00002134, 0x00002133, 0x00002132, 0x00002131,
	0x00002130, 0x0000212E, 0x0000212D, 0x0000212C, 0x0000212B, 0x0000212A, 0x00002129, 0x00002128,
	0x00002126, 0x00002125, 0x00002124, 0x00002123, 0x00002122, 0x00002121, 0x00002120, 0x0000211E,
	0x0000211D, 0x0000211C, 0x0000211B, 0x0000211A, 0x00002119, 0x00002118, 0x00002116, 0x00002115,
	0x00002114, 0x00002113, 0x00002112, 0x00002111, 0x00002110, 0x0000210E, 0x0000210D, 0x0000210C,
	0x0000210B, 0x0000210A, 0x00002109, 0x00002108, 0x00002107, 0x00002105, 0x00002104, 0x00002103,
	0x00002102, 0x00002101, 0x00002100, 0x000020FF, 0x000020FD, 0x000020FC, 0x000020FB, 0x000020FA,
	0x000020F9, 0x000020F8, 0x000020F7, 0x000020F6, 0x000020F4, 0x000020F3, 0x000020F2, 0x000020F1,
	0x000020F0, 0x000020EF, 0x000020EE, 0x000020EC, 0x000020EB, 0x000020EA, 0x000020E9, 0x000020E8,
	0x000020E7, 0x000020E6, 0x000020E5, 0x000020E3, 0x000020E2, 0x000020E1, 0x000020E0, 0x000020DF,
	0x000020DE, 0x000020DD, 0x000020DB, 0x000020DA, 0x000020D9, 0x000020D8, 0x000020D7, 0x000020D6,
	0x000020D5, 0x000020D4, 0x000020D2, 0x000020D1, 0x000020D0, 0x000020CF, 0x000020CE, 0x000020CD,
	0x000020CC, 0x000020CB, 0x000020C9, 0x000020C8, 0x000020C7, 0x000020C6, 0x000020C5, 0x000020C4,
	0x000020C3, 0x000020C2, 0x000020C0, 0x000020BF, 0x000020BE, 0x000020BD, 0x000020BC, 0x000020BB,
	0x000020BA, 0x000020B9, 0x000020B7, 0x000020B6, 0x000020B5, 0x000020B4, 0x000020B3, 0x000020B2,
	0x000020B1, 0x000020B0, 0x000020AE, 0x000020AD, 0x000020AC, 0x000020AB, 0x000020AA, 0x000020A9,
	0x000020A8, 0x000020A7, 0x000020A5, 0x000020A4, 0x000020A3, 0x000020A2, 0x000020A1, 0x000020A0,
	0x0000209F, 0x0000209E, 0x0000209D, 0x0000209B, 0x0000209A, 0x00002099, 0x00002098, 0x00002097,
	0x00002096, 0x00002095, 0x00002094, 0x00002092, 0x00002091, 0x00002090, 0x0000208F, 0x0000208E,
	0x0000208D, 0x0000208C, 0x0000208B, 0x00002089, 0x00002088, 0x00002087, 0x00002086, 0x00002085,
	0x00002084, 0x00002083, 0x00002082, 0x00002081, 0x0000207F, 0x0000207E, 0x0000207D, 0x0000207C,
	0x0000207B, 0x0000207A, 0x00002079, 0x00002078, 0x00002077, 0x00002075, 0x00002074, 0x00002073,
	0x00002072, 0x00002071, 0x00002070, 0x0000206F, 0x0000206E, 0x0000206C, 0x0000206B, 0x0000206A,
	0x00002069, 0x00002068, 0x00002067, 0x00002066, 0x00002065, 0x00002064, 0x00002062, 0x00002061,
	0x00002060, 0x0000205F, 0x0000205E, 0x0000205D, 0x0000205C, 0x0000205B, 0x0000205A, 0x00002058,
	0x00002057, 0x00002056, 0x00002055, 0x00002054, 0x00002053, 0x00002052, 0x00002051, 0x00002050,
	0x0000204E, 0x0000204D, 0x0000204C, 0x0000204B, 0x0000204A, 0x00002049, 0x00002048, 0x00002047,
	0x00002046, 0x00002044, 0x00002043, 0x00002042, 0x00002041, 0x00002040, 0x0000203F, 0x0000203E,
	0x0000203D, 0x0000203C, 0x0000203B, 0x00002039, 0x00002038, 0x00002037, 0x00002036, 0x00002035,
	0x00002034, 0x00002033, 0x00002032, 0x00002031, 0x0000202F, 0x0000202E, 0x0000202D, 0x0000202C,
	0x0000202B, 0x0000202A, 0x00002029, 0x00002028, 0x00002027, 0x00002026, 0x00002024, 0x00002023,
	0x00002022, 0x00002021, 0x00002020, 0x0000201F, 0x0000201E, 0x0000201D, 0x0000201C, 0x0000201A,
	0x00002019, 0x00002018, 0x00002017, 0x00002016, 0x00002015, 0x00002014, 0x00002013, 0x00002012,
	0x00002011, 0x0000200F, 0x0000200E, 0x0000200D, 0x0000200C, 0x0000200B, 0x0000200A, 0x00002009,
	0x00002008, 0x00002007, 0x00002006, 0x00002004, 0x00002003, 0x00002002, 0x00002001, 0x00002000,
	0x00001FFF, 0x00001FFE, 0x00001FFD, 0x00001FFC, 0x00001FFB, 0x00001FF9, 0x00001FF8, 0x00001FF7,
	0x00001FF6, 0x00001FF5, 0x00001FF4, 0x00001FF3, 0x00001FF2, 0x00001FF1, 0x00001FF0, 0x00001FEE,
	0x00001FED, 0x00001FEC, 0x00001FEB, 0x00001FEA, 0x00001FE9, 0x00001FE8, 0x00001FE7, 0x00001FE6,
	0x00001FE5, 0x00001FE3, 0x00001FE2, 0x00001FE1, 0x00001FE0, 0x00001FDF, 0x00001FDE, 0x00001FDD,
	0x00001FDC, 0x00001FDB, 0x00001FDA, 0x00001FD9, 0x00001FD7, 0x00001FD6, 0x00001FD5, 0x00001FD4,
	0x00001FD3, 0x00001FD2, 0x00001FD1, 0x00001FD0, 0x00001FCF, 0x00001FCE, 0x00001FCC, 0x00001FCB,
	0x00001FCA, 0x00001FC9, 0x00001FC8, 0x00001FC7, 0x00001FC6, 0x00001FC5, 0x00001FC4, 0x00001FC3,
	0x00001FC2, 0x00001FC0, 0x00001FBF, 0x00001FBE, 0x00001FBD, 0x00001FBC, 0x00001FBB, 0x00001FBA,
	0x00001FB9, 0x00001FB8, 0x00001FB7, 0x00001FB6, 0x00001FB4, 0x00001FB3, 0x00001FB2, 0x00001FB1,
	0x00001FB0, 0x00001FAF, 0x00001FAE, 0x00001FAD, 0x00001FAC, 0x00001FAB, 0x00001FAA, 0x00001FA8,
	0x00001FA7, 0x00001FA6, 0x00001FA5, 0x00001FA4, 0x00001FA3, 0x00001FA2, 0x00001FA1, 0x00001FA0,
	0x00001F9F, 0x00001F9E, 0x00001F9C, 0x00001F9B, 0x00001F9A, 0x00001F99, 0x00001F98, 0x00001F97,
	0x00001F96, 0x00001F95, 0x00001F94, 0x00001F93, 0x00001F92, 0x00001F91, 0x00001F8F, 0x00001F8E,
	0x00001F8D, 0x00001F8C, 0x00001F8B, 0x00001F8A, 0x00001F89, 0x00001F88, 0x00001F87, 0x00001F86,
	0x00001F85, 0x00001F84, 0x00001F82, 0x00001F81, 0x00001F80, 0x00001F7F, 0x00001F7E, 0x00001F7D,
	0x00001F7C, 0x00001F7B, 0x00001F7A, 0x00001F79, 0x00001F78, 0x00001F76, 0x00001F75, 0x00001F74,
	0x00001F73, 0x00001F72, 0x00001F71, 0x00001F70, 0x00001F6F, 0x00001F6E, 0x00001F6D, 0x00001F6C,
	0x00001F6B, 0x00001F69, 0x00001F68, 0x00001F67, 0x00001F66, 0x00001F65, 0x00001F64, 0x00001F63,
	0x00001F62, 0x00001F61, 0x00001F60, 0x00001F5F, 0x00001F5E, 0x00001F5D, 0x00001F5B, 0x00001F5A,
	0x00001F59, 0x00001F58, 0x00001F57, 0x00001F56, 0x00001F55, 0x00001F54, 0x00001F53, 0x00001F52,
	0x00001F51, 0x00001F50, 0x00001F4E, 0x00001F4D, 0x00001F4C, 0x00001F4B, 0x00001F4A, 0x00001F49,
	0x00001F48, 0x00001F47, 0x00001F46, 0x00001F45, 0x00001F44, 0x00001F43, 0x00001F42, 0x00001F40,
	0x00001F3F, 0x00001F3E, 0x00001F3D, 0x00001F3C, 0x00001F3B, 0x00001F3A, 0x00001F39, 0x00001F38,
	0x00001F37, 0x00001F36, 0x00001F35, 0x00001F34, 0x00001F32, 0x00001F31, 0x00001F30, 0x00001F2F,
	0x00001F2E, 0x00001F2D, 0x00001F2C, 0x00001F2B, 0x00001F2A, 0x00001F29, 0x00001F28, 0x00001F27,
	0x00001F26, 0x00001F24, 0x00001F23, 0x00001F22, 0x00001F21, 0x00001F20, 0x00001F1F, 0x00001F1E,
	0x00001F1D, 0x00001F1C, 0x00001F1B, 0x00001F1A, 0x00001F19, 0x00001F18, 0x00001F16, 0x00001F15,
	0x00001F14, 0x00001F13, 0x00001F12, 0x00001F11, 0x00001F10, 0x00001F0F, 0x00001F0E, 0x00001F0D,
	0x00001F0C, 0x00001F0B, 0x00001F0A, 0x00001F09, 0x00001F07, 0x00001F06, 0x00001F05, 0x00001F04,
	0x00001F03, 0x00001F02, 0x00001F01, 0x00001F00, 0x00001EFF, 0x00001EFE, 0x00001EFD, 0x00001EFC,
	0x00001EFB, 0x00001EFA, 0x00001EF8, 0x00001EF7, 0x00001EF6, 0x00001EF5, 0x00001EF4, 0x00001EF3,
	0x00001EF2, 0x00001EF1, 0x00001EF0, 0x00001EEF, 0x00001EEE, 0x00001EED, 0x00001EEC, 0x00001EEB,
	0x00001EE9, 0x00001EE8, 0x00001EE7, 0x00001EE6, 0x00001EE5, 0x00001EE4, 0x00001EE3, 0x00001EE2,
	0x00001EE1, 0x00001EE0, 0x00001EDF, 0x00001EDE, 0x00001EDD, 0x00001EDC, 0x00001EDB, 0x00001ED9,
	0x00001ED8, 0x00001ED7, 0x00001ED6, 0x00001ED5, 0x00001ED4, 0x00001ED3, 0x00001ED2, 0x00001ED1,
	0x00001ED0, 0x00001ECF, 0x00001ECE, 0x00001ECD, 0x00001ECC, 0x00001ECB, 0x00001EC9, 0x00001EC8,
	0x00001EC7, 0x00001EC6, 0x00001EC5, 0x00001EC4, 0x00001EC3, 0x00001EC2, 0x00001EC1, 0x00001EC0,
	0x00001EBF, 0x00001EBE, 0x00001EBD, 0x00001EBC, 0x00001EBB, 0x00001EB9, 0x00001EB8, 0x00001EB7,
	0x00001EB6, 0x00001EB5, 0x00001EB4, 0x00001EB3, 0x00001EB2, 0x00001EB1, 0x00001EB0, 0x00001EAF,
	0x00001EAE, 0x00001EAD, 0x00001EAC, 0x00001EAB, 0x00001EAA, 0x00001EA8, 0x00001EA7, 0x00001EA6,
	0x00001EA5, 0x00001EA4, 0x00001EA3, 0x00001EA2, 0x00001EA1, 0x00001EA0, 0x00001E9F, 0x00001E9E,
	0x00001E9D, 0x00001E9C, 0x00001E9B, 0x00001E9A, 0x00001E99, 0x00001E97, 0x00001E96, 0x00001E95,
	0x00001E94, 0x00001E93, 0x00001E92, 0x00001E91, 0x00001E90, 0x00001E8F, 0x00001E8E, 0x00001E8D,
	0x00001E8C, 0x00001E8B, 0x00001E8A, 0x00001E89, 0x00001E88, 0x00001E87, 0x00001E85, 0x00001E84,
	0x00001E83, 0x00001E82, 0x00001E81, 0x00001E80, 0x00001E7F, 0x00001E7E, 0x00001E7D, 0x00001E7C,
	0x00001E7B, 0x00001E7A, 0x00001E79, 0x00001E78, 0x00001E77, 0x00001E76, 0x00001E75, 0x00001E73,
	0x00001E72, 0x00001E71, 0x00001E70, 0x00001E6F, 0x00001E6E, 0x00001E6D, 0x00001E6C, 0x00001E6B,
	0x00001E6A, 0x00001E69, 0x00001E68, 0x00001E67, 0x00001E66, 0x00001E65, 0x00001E64, 0x00001E63,
	0x00001E61, 0x00001E60, 0x00001E5F, 0x00001E5E, 0x00001E5D, 0x00001E5C, 0x00001E5B, 0x00001E5A,
	0x00001E59, 0x00001E58, 0x00001E57, 0x00001E56, 0x00001E55, 0x00001E54, 0x00001E53, 0x00001E52,
	0x00001E51, 0x00001E50, 0x00001E4E, 0x00001E4D, 0x00001E4C, 0x00001E4B, 0x00001E4A, 0x00001E49,
	0x00001E48, 0x00001E47, 0x00001E46, 0x00001E45, 0x00001E44, 0x00001E43, 0x00001E42, 0x00001E41,
	0x00001E40, 0x00001E3F, 0x00001E3E, 0x00001E3D, 0x00001E3C, 0x00001E3A, 0x00001E39, 0x00001E38,
	0x00001E37, 0x00001E36, 0x00001E35, 0x00001E34, 0x00001E33, 0x00001E32, 0x00001E31, 0x00001E30,
	0x00001E2F, 0x00001E2E, 0x00001E2D, 0x00001E2C, 0x00001E2B, 0x00001E2A, 0x00001E29, 0x00001E28,
	0x00001E26, 0x00001E25, 0x00001E24, 0x00001E23, 0x00001E22, 0x00001E21, 0x00001E20, 0x00001E1F,
	0x00001E1E, 0x00001E1D, 0x00001E1C, 0x00001E1B, 0x00001E1A, 0x00001E19, 0x00001E18, 0x00001E17,
	0x00001E16, 0x00001E15, 0x00001E14, 0x00001E13, 0x00001E11, 0x00001E10, 0x00001E0F, 0x00001E0E,
	0x00001E0D, 0x00001E0C, 0x00001E0B, 0x00001E0A, 0x00001E09, 0x00001E08, 0x00001E07, 0x00001E06,
	0x00001E05, 0x00001E04, 0x00001E03, 0x00001E02, 0x00001E01, 0x00001E00, 0x00001DFF, 0x00001DFE,
	0x00001DFC, 0x00001DFB, 0x00001DFA, 0x00001DF9, 0x00001DF8, 0x00001DF7, 0x00001DF6, 0x00001DF5,
	0x00001DF4, 0x00001DF3, 0x00001DF2, 0x00001DF1, 0x00001DF0, 0x00001DEF, 0x00001DEE, 0x00001DED,
	0x00001DEC, 0x00001DEB, 0x00001DEA, 0x00001DE9, 0x00001DE8, 0x00001DE7, 0x00001DE5, 0x00001DE4,
	0x00001DE3, 0x00001DE2, 0x00001DE1, 0x00001DE0, 0x00001DDF, 0x00001DDE, 0x00001DDD, 0x00001DDC,
	0x00001DDB, 0x00001DDA, 0x00001DD9, 0x00001DD8, 0x00001DD7, 0x00001DD6, 0x00001DD5, 0x00001DD4,
	0x00001DD3, 0x00001DD2, 0x00001DD1, 0x00001DD0, 0x00001DCE, 0x00001DCD, 0x00001DCC, 0x00001DCB,
	0x00001DCA, 0x00001DC9, 0x00001DC8, 0x00001DC7, 0x00001DC6, 0x00001DC5, 0x00001DC4, 0x00001DC3,
	0x00001DC2, 0x00001DC1, 0x00001DC0, 0x00001DBF, 0x00001DBE, 0x00001DBD, 0x00001DBC, 0x00001DBB,
	0x00001DBA, 0x00001DB9, 0x00001DB8, 0x00001DB6, 0x00001DB5, 0x00001DB4, 0x00001DB3, 0x00001DB2,
	0x00001DB1, 0x00001DB0, 0x00001DAF, 0x00001DAE, 0x00001DAD, 0x00001DAC, 0x00001DAB, 0x00001DAA,
	0x00001DA9, 0x00001DA8, 0x00001DA7, 0x00001DA6, 0x00001DA5, 0x00001DA4, 0x00001DA3, 0x00001DA2,
	0x00001DA1, 0x00001DA0, 0x00001D9F, 0x00001D9D, 0x00001D9C, 0x00001D9B, 0x00001D9A, 0x00001D99,
	0x00001D98, 0x00001D97, 0x00001D96, 0x00001D95, 0x00001D94, 0x00001D93, 0x00001D92, 0x00001D91,
	0x00001D90, 0x00001D8F, 0x00001D8E, 0x00001D8D, 0x00001D8C, 0x00001D8B, 0x00001D8A, 0x00001D89,
	0x00001D88, 0x00001D87, 0x00001D86, 0x00001D85, 0x00001D83, 0x00001D82, 0x00001D81, 0x00001D80,
	0x00001D7F, 0x00001D7E, 0x00001D7D, 0x00001D7C, 0x00001D7B, 0x00001D7A, 0x00001D79, 0x00001D78,
	0x00001D77, 0x00001D76, 0x00001D75, 0x00001D74, 0x00001D73, 0x00001D72, 0x00001D71, 0x00001D70,
	0x00001D6F, 0x00001D6E, 0x00001D6D, 0x00001D6C, 0x00001D6B, 0x00001D6A, 0x00001D68, 0x00001D67,
	0x00001D66, 0x00001D65, 0x00001D64, 0x00001D63, 0x00001D62, 0x00001D61, 0x00001D60, 0x00001D5F,
	0x00001D5E, 0x00001D5D, 0x00001D5C, 0x00001D5B, 0x00001D5A, 0x00001D59, 0x00001D58, 0x00001D57,
	0x00001D56, 0x00001D55, 0x00001D54, 0x00001D53, 0x00001D52, 0x00001D51, 0x00001D50, 0x00001D4F,
	0x00001D4E, 0x00001D4D, 0x00001D4B, 0x00001D4A, 0x00001D49, 0x00001D48, 0x00001D47, 0x00001D46,
	0x00001D45, 0x00001D44, 0x00001D43, 0x00001D42, 0x00001D41, 0x00001D40, 0x00001D3F, 0x00001D3E,
	0x00001D3D, 0x00001D3C, 0x00001D3B, 0x00001D3A, 0x00001D39, 0x00001D38, 0x00001D37, 0x00001D36,
	0x00001D35, 0x00001D34, 0x00001D33, 0x00001D32, 0x00001D31, 0x00001D30, 0x00001D2F, 0x00001D2E,
	0x00001D2C, 0x00001D2B, 0x00001D2A, 0x00001D29, 0x00001D28, 0x00001D27, 0x00001D26, 0x00001D25,
	0x00001D24, 0x00001D23, 0x00001D22, 0x00001D21, 0x00001D20, 0x00001D1F, 0x00001D1E, 0x00001D1D,
	0x00001D1C, 0x00001D1B, 0x00001D1A, 0x00001D19, 0x00001D18, 0x00001D17, 0x00001D16, 0x00001D15,
	0x00001D14, 0x00001D13, 0x00001D12, 0x00001D11, 0x00001D10, 0x00001D0F, 0x00001D0E, 0x00001D0C,
	0x00001D0B, 0x00001D0A, 0x00001D09, 0x00001D08, 0x00001D07, 0x00001D06, 0x00001D05, 0x00001D04,
	0x00001D03, 0x00001D02, 0x00001D01, 0x00001D00, 0x00001CFF, 0x00001CFE, 0x00001CFD, 0x00001CFC,
	0x00001CFB, 0x00001CFA, 0x00001CF9, 0x00001CF8, 0x00001CF7, 0x00001CF6, 0x00001CF5, 0x00001CF4,
	0x00001CF3, 0x00001CF2, 0x00001CF1, 0x00001CF0, 0x00001CEF, 0x00001CEE, 0x00001CED, 0x00001CEC,
	0x00001CEB, 0x00001CE9, 0x00001CE8, 0x00001CE7, 0x00001CE6, 0x00001CE5, 0x00001CE4, 0x00001CE3,
	0x00001CE2, 0x00001CE1, 0x00001CE0, 0x00001CDF, 0x00001CDE, 0x00001CDD, 0x00001CDC, 0x00001CDB,
	0x00001CDA, 0x00001CD9, 0x00001CD8, 0x00001CD7, 0x00001CD6, 0x00001CD5, 0x00001CD4, 0x00001CD3,
	0x00001CD2, 0x00001CD1, 0x00001CD0, 0x00001CCF, 0x00001CCE, 0x00001CCD, 0x00001CCC, 0x00001CCB,
	0x00001CCA, 0x00001CC9, 0x00001CC8, 0x00001CC7, 0x00001CC6, 0x00001CC4, 0x00001CC3, 0x00001CC2,
	0x00001CC1, 0x00001CC0, 0x00001CBF, 0x00001CBE, 0x00001CBD, 0x00001CBC, 0x00001CBB, 0x00001CBA,
	0x00001CB9, 0x00001CB8, 0x00001CB7, 0x00001CB6, 0x00001CB5, 0x00001CB4, 0x00001CB3, 0x00001CB2,
	0x00001CB1, 0x00001CB0, 0x00001CAF, 0x00001CAE, 0x00001CAD, 0x00001CAC, 0x00001CAB, 0x00001CAA,
	0x00001CA9, 0x00001CA8, 0x00001CA7, 0x00001CA6, 0x00001CA5, 0x00001CA4, 0x00001CA3, 0x00001CA2,
	0x00001CA1, 0x00001CA0, 0x00001C9F, 0x00001C9E, 0x00001C9D, 0x00001C9B, 0x00001C9A, 0x00001C99,
	0x00001C98, 0x00001C97, 0x00001C96, 0x00001C95, 0x00001C94, 0x00001C93, 0x00001C92, 0x00001C91,
	0x00001C90, 0x00001C8F, 0x00001C8E, 0x00001C8D, 0x00001C8C, 0x00001C8B, 0x00001C8A, 0x00001C89,
	0x00001C88, 0x00001C87, 0x00001C86, 0x00001C85, 0x00001C84, 0x00001C83, 0x00001C82, 0x00001C81,
	0x00001C80, 0x00001C7F, 0x00001C7E, 0x00001C7D, 0x00001C7C, 0x00001C7B, 0x00001C7A, 0x00001C79,
	0x00001C78, 0x00001C77, 0x00001C76, 0x00001C75, 0x00001C74, 0x00001C73, 0x00001C72, 0x00001C71,
	0x00001C70, 0x00001C6E, 0x00001C6D, 0x00001C6C, 0x00001C6B, 0x00001C6A, 0x00001C69, 0x00001C68,
	0x00001C67, 0x00001C66, 0x00001C65, 0x00001C64, 0x00001C63, 0x00001C62, 0x00001C61, 0x00001C60,
	0x00001C5F, 0x00001C5E, 0x00001C5D, 0x00001C5C, 0x00001C5B, 0x00001C5A, 0x00001C59, 0x00001C58,
	0x00001C57, 0x00001C56, 0x00001C55, 0x00001C54, 0x00001C53, 0x00001C52, 0x00001C51, 0x00001C50,
	0x00001C4F, 0x00001C4E, 0x00001C4D, 0x00001C4C, 0x00001C4B, 0x00001C4A, 0x00001C49, 0x00001C48,
	0x00001C47, 0x00001C46, 0x00001C45, 0x00001C44, 0x00001C43, 0x00001C42, 0x00001C41, 0x00001C40,
	0x00001C3F, 0x00001C3E, 0x00001C3C, 0x00001C3B, 0x00001C3A, 0x00001C39, 0x00001C38, 0x00001C37,
	0x00001C36, 0x00001C35, 0x00001C34, 0x00001C33, 0x00001C32, 0x00001C31, 0x00001C30, 0x00001C2F,
	0x00001C2E, 0x00001C2D, 0x00001C2C, 0x00001C2B, 0x00001C2A, 0x00001C29, 0x00001C28, 0x00001C27,
	0x00001C26, 0x00001C25, 0x00001C24, 0x00001C23, 0x00001C22, 0x00001C21, 0x00001C20, 0x00001C1F,
	0x00001C1E, 0x00001C1D, 0x00001C1C, 0x00001C1B, 0x00001C1A, 0x00001C19, 0x00001C18, 0x00001C17,
	0x00001C16, 0x00001C15, 0x00001C14, 0x00001C13, 0x00001C12, 0x00001C11, 0x00001C10, 0x00001C0F,
	0x00001C0E, 0x00001C0D, 0x00001C0C, 0x00001C0B, 0x00001C0A, 0x00001C09, 0x00001C08, 0x00001C07,
	0x00001C06, 0x00001C05, 0x00001C03, 0x00001C02, 0x00001C01, 0x00001C00, 0x00001BFF, 0x00001BFE,
	0x00001BFD, 0x00001BFC, 0x00001BFB, 0x00001BFA, 0x00001BF9, 0x00001BF8, 0x00001BF7, 0x00001BF6,
	0x00001BF5, 0x00001BF4, 0x00001BF3, 0x00001BF2, 0x00001BF1, 0x00001BF0, 0x00001BEF, 0x00001BEE,
	0x00001BED, 0x00001BEC, 0x00001BEB, 0x00001BEA, 0x00001BE9, 0x00001BE8, 0x00001BE7, 0x00001BE6,
	0x00001BE5, 0x00001BE4, 0x00001BE3, 0x00001BE2, 0x00001BE1, 0x00001BE0, 0x00001BDF, 0x00001BDE,
	0x00001BDD, 0x00001BDC, 0x00001BDB, 0x00001BDA, 0x00001BD9, 0x00001BD8, 0x00001BD7, 0x00001BD6,
	0x00001BD5, 0x00001BD4, 0x00001BD3, 0x00001BD2, 0x00001BD1, 0x00001BD0, 0x00001BCF, 0x00001BCE,
	0x00001BCD, 0x00001BCC, 0x00001BCB, 0x00001BCA, 0x00001BC9, 0x00001BC8, 0x00001BC7, 0x00001BC6,
	0x00001BC5, 0x00001BC4, 0x00001BC3, 0x00001BC2, 0x00001BC0, 0x00001BBF, 0x00001BBE, 0x00001BBD,
	0x00001BBC, 0x00001BBB, 0x00001BBA, 0x00001BB9, 0x00001BB8, 0x00001BB7, 0x00001BB6, 0x00001BB5,
	0x00001BB4, 0x00001BB3, 0x00001BB2, 0x00001BB1, 0x00001BB0, 0x00001BAF, 0x00001BAE, 0x00001BAD,
	0x00001BAC, 0x00001BAB, 0x00001BAA, 0x00001BA9, 0x00001BA8, 0x00001BA7, 0x00001BA6, 0x00001BA5,
	0x00001BA4, 0x00001BA3, 0x00001BA2, 0x00001BA1, 0x00001BA0, 0x00001B9F, 0x00001B9E, 0x00001B9D,
	0x00001B9C, 0x00001B9B, 0x00001B9A, 0x00001B99, 0x00001B98, 0x00001B97, 0x00001B96, 0x00001B95,
	0x00001B94, 0x00001B93, 0x00001B92, 0x00001B91, 0x00001B90, 0x00001B8F, 0x00001B8E, 0x00001B8D,
	0x00001B8C, 0x00001B8B, 0x00001B8A, 0x00001B89, 0x00001B88, 0x00001B87, 0x00001B86, 0x00001B85,
	0x00001B84, 0x00001B83, 0x00001B82, 0x00001B81, 0x00001B80, 0x00001B7F, 0x00001B7E, 0x00001B7D,
	0x00001B7C, 0x00001B7B, 0x00001B7A, 0x00001B79, 0x00001B78, 0x00001B77, 0x00001B76, 0x00001B75,
	0x00001B74, 0x00001B73, 0x00001B72, 0x00001B71, 0x00001B70, 0x00001B6F, 0x00001B6E, 0x00001B6D,
	0x00001B6B, 0x00001B6A, 0x00001B69, 0x00001B68, 0x00001B67, 0x00001B66, 0x00001B65, 0x00001B64,
	0x00001B63, 0x00001B62, 0x00001B61, 0x00001B60, 0x00001B5F, 0x00001B5E, 0x00001B5D, 0x00001B5C,
	0x00001B5B, 0x00001B5A, 0x00001B59, 0x00001B58, 0x00001B57, 0x00001B56, 0x00001B55, 0x00001B54,
	0x00001B53, 0x00001B52, 0x00001B51, 0x00001B50, 0x00001B4F, 0x00001B4E, 0x00001B4D, 0x00001B4C,
	0x00001B4B, 0x00001B4A, 0x00001B49, 0x00001B48, 0x00001B47, 0x00001B46, 0x00001B45, 0x00001B44,
	0x00001B43, 0x00001B42, 0x00001B41, 0x00001B40, 0x00001B3F, 0x00001B3E, 0x00001B3D, 0x00001B3C,
	0x00001B3B, 0x00001B3A, 0x00001B39, 0x00001B38, 0x00001B37, 0x00001B36, 0x00001B35, 0x00001B34,
	0x00001B33, 0x00001B32, 0x00001B31, 0x00001B30, 0x00001B2F, 0x00001B2E, 0x00001B2D, 0x00001B2C,
	0x00001B2B, 0x00001B2A, 0x00001B29, 0x00001B28, 0x00001B27, 0x00001B26, 0x00001B25, 0x00001B24,
	0x00001B23, 0x00001B22, 0x00001B21, 0x00001B20, 0x00001B1F, 0x00001B1E, 0x00001B1D, 0x00001B1C,
	0x00001B1B, 0x00001B1A, 0x00001B19, 0x00001B18, 0x00001B17, 0x00001B16, 0x00001B15, 0x00001B14,
	0x00001B13, 0x00001B12, 0x00001B11, 0x00001B10, 0x00001B0F, 0x00001B0E, 0x00001B0D, 0x00001B0C,
	0x00001B0B, 0x00001B0A, 0x00001B09, 0x00001B08, 0x00001B07, 0x00001B06, 0x00001B05, 0x00001B04,
	0x00001B03, 0x00001B02, 0x00001B01, 0x00001B00, 0x00001AFF, 0x00001AFE, 0x00001AFD, 0x00001AFC,
	0x00001AFB, 0x00001AFA, 0x00001AF9, 0x00001AF8, 0x00001AF7, 0x00001AF6, 0x00001AF5, 0x00001AF4,
	0x00001AF2, 0x00001AF1, 0x00001AF0, 0x00001AEF, 0x00001AEE, 0x00001AED, 0x00001AEC, 0x00001AEB,
	0x00001AEA, 0x00001AE9, 0x00001AE8, 0x00001AE7, 0x00001AE6, 0x00001AE5, 0x00001AE4, 0x00001AE3,
	0x00001AE2, 0x00001AE1, 0x00001AE0, 0x00001ADF, 0x00001ADE, 0x00001ADD, 0x00001ADC, 0x00001ADB,
	0x00001ADA, 0x00001AD9, 0x00001AD8, 0x00001AD7, 0x00001AD6, 0x00001AD5, 0x00001AD4, 0x00001AD3,
	0x00001AD2, 0x00001AD1, 0x00001AD0, 0x00001ACF, 0x00001ACE, 0x00001ACD, 0x00001ACC, 0x00001ACB,
	0x00001ACA, 0x00001AC9, 0x00001AC8, 0x00001AC7, 0x00001AC6, 0x00001AC5, 0x00001AC4, 0x00001AC3,
	0x00001AC2, 0x00001AC1, 0x00001AC0, 0x00001ABF, 0x00001ABE, 0x00001ABD, 0x00001ABC, 0x00001ABB,
	0x00001ABA, 0x00001AB9, 0x00001AB8, 0x00001AB7, 0x00001AB6, 0x00001AB5, 0x00001AB4, 0x00001AB3,
	0x00001AB2, 0x00001AB1, 0x00001AB0, 0x00001AAF, 0x00001AAE, 0x00001AAD, 0x00001AAC, 0x00001AAB,
	0x00001AAA, 0x00001AA9, 0x00001AA8, 0x00001AA7, 0x00001AA6, 0x00001AA5, 0x00001AA4, 0x00001AA3,
	0x00001AA2, 0x00001AA1, 0x00001AA0, 0x00001A9F, 0x00001A9E, 0x00001A9D, 0x00001A9C, 0x00001A9B,
	0x00001A9A, 0x00001A99, 0x00001A98, 0x00001A97, 0x00001A96, 0x00001A95, 0x00001A94, 0x00001A93,
	0x00001A92, 0x00001A91, 0x00001A90, 0x00001A8F, 0x00001A8E, 0x00001A8D, 0x00001A8C, 0x00001A8B,
	0x00001A8A, 0x00001A89, 0x00001A88, 0x00001A87, 0x00001A86, 0x00001A85, 0x00001A84, 0x00001A83,
	0x00001A82, 0x00001A81, 0x00001A80, 0x00001A7F, 0x00001A7E, 0x00001A7D, 0x00001A7C, 0x00001A7B,
	0x00001A7A, 0x00001A79, 0x00001A78, 0x00001A77, 0x00001A76, 0x00001A75, 0x00001A74, 0x00001A73,
	0x00001A72, 0x00001A71, 0x00001A70, 0x00001A6F, 0x00001A6E, 0x00001A6D, 0x00001A6C, 0x00001A6B,
	0x00001A6A, 0x00001A69, 0x00001A68, 0x00001A67, 0x00001A66, 0x00001A65, 0x00001A64, 0x00001A63,
	0x00001A62, 0x00001A61, 0x00001A60, 0x00001A5F, 0x00001A5E, 0x00001A5D, 0x00001A5C, 0x00001A5B,
	0x00001A5A, 0x00001A59, 0x00001A58, 0x00001A57, 0x00001A56, 0x00001A55, 0x00001A54, 0x00001A53,
	0x00001A52, 0x00001A51, 0x00001A50, 0x00001A4F, 0x00001A4E, 0x00001A4D, 0x00001A4C, 0x00001A4B,
	0x00001A4A, 0x00001A49, 0x00001A48, 0x00001A47, 0x00001A46, 0x00001A45, 0x00001A44, 0x00001A43,
	0x00001A42, 0x00001A41, 0x00001A40, 0x00001A3F, 0x00001A3E, 0x00001A3D, 0x00001A3C, 0x00001A3B,
	0x00001A3A, 0x00001A39, 0x00001A38, 0x00001A37, 0x00001A36, 0x00001A35, 0x00001A34, 0x00001A33,
	0x00001A32, 0x00001A31, 0x00001A30, 0x00001A2F, 0x00001A2E, 0x00001A2D, 0x00001A2C, 0x00001A2B,
	0x00001A2A, 0x00001A29, 0x00001A28, 0x00001A27, 0x00001A26, 0x00001A25, 0x00001A24, 0x00001A23,
	0x00001A22, 0x00001A21, 0x00001A20, 0x00001A1F, 0x00001A1E, 0x00001A1D, 0x00001A1C, 0x00001A1B,
	0x00001A1A, 0x00001A19, 0x00001A18, 0x00001A17, 0x00001A16, 0x00001A15, 0x00001A14, 0x00001A13,
	0x00001A12, 0x00001A11, 0x00001A10, 0x00001A0F, 0x00001A0E, 0x00001A0D, 0x00001A0C, 0x00001A0B,
	0x00001A0A, 0x00001A09, 0x00001A08, 0x00001A07, 0x00001A06, 0x00001A05, 0x00001A04, 0x00001A03,
	0x00001A02, 0x00001A01, 0x00001A00, 0x000019FF, 0x000019FE, 0x000019FD, 0x000019FC, 0x000019FB,
	0x000019FA, 0x000019F9, 0x000019F8, 0x000019F7, 0x000019F6, 0x000019F5, 0x000019F4, 0x000019F3,
	0x000019F2, 0x000019F1, 0x000019F0, 0x000019EF, 0x000019EE, 0x000019ED, 0x000019EC, 0x000019EB,
	0x000019EA, 0x000019E9, 0x000019E8, 0x000019E7, 0x000019E6, 0x000019E5, 0x000019E4, 0x000019E3,
	0x000019E2, 0x000019E1, 0x000019E0, 0x000019DF, 0x000019DE, 0x000019DD, 0x000019DC, 0x000019DB,
	0x000019DA, 0x000019D9, 0x000019D8, 0x000019D7, 0x000019D6, 0x000019D5, 0x000019D4, 0x000019D3,
	0x000019D2, 0x000019D1, 0x000019D0, 0x000019CF, 0x000019CE, 0x000019CD, 0x000019CC, 0x000019CB,
	0x000019CA, 0x000019C9, 0x000019C8, 0x000019C7, 0x000019C6, 0x000019C5, 0x000019C4, 0x000019C3,
	0x000019C2, 0x000019C1, 0x000019C0, 0x000019BF, 0x000019BE, 0x000019BD, 0x000019BC, 0x000019BB,
	0x000019BA, 0x000019B9, 0x000019B8, 0x000019B7, 0x000019B6, 0x000019B5, 0x000019B4, 0x000019B3,
	0x000019B2, 0x000019B1, 0x000019B0, 0x000019AF, 0x000019AE, 0x000019AD, 0x000019AC, 0x000019AB,
	0x000019AA, 0x000019A9, 0x000019A8, 0x000019A7, 0x000019A6, 0x000019A5, 0x000019A4, 0x000019A3,
	0x000019A2, 0x000019A1, 0x000019A0, 0x0000199F, 0x0000199E, 0x0000199D, 0x0000199C, 0x0000199B,
	0x0000199A, 0x00001999, 0x00001998, 0x00001997, 0x00001996, 0x00001995, 0x00001994, 0x00001993,
	0x00001992, 0x00001991, 0x00001990, 0x0000198F, 0x0000198E, 0x0000198D, 0x0000198C, 0x0000198B,
	0x0000198A, 0x00001989, 0x00001988, 0x00001987, 0x00001986, 0x00001985, 0x00001984, 0x00001983,
	0x00001982, 0x00001981, 0x00001980, 0x0000197F, 0x0000197E, 0x0000197D, 0x0000197C, 0x0000197B,
	0x0000197A, 0x00001979, 0x00001978, 0x00001977, 0x00001976, 0x00001975, 0x00001974, 0x00001973,
	0x00001972, 0x00001971, 0x00001970, 0x0000196F, 0x0000196E, 0x0000196D, 0x0000196C, 0x0000196B,
	0x0000196A, 0x00001969, 0x00001968, 0x00001967, 0x00001966, 0x00001965, 0x00001964, 0x00001963,
	0x00001962, 0x00001961, 0x00001960, 0x0000195F, 0x0000195E, 0x0000195D, 0x0000195C, 0x0000195B,
	0x0000195A, 0x00001959, 0x00001958, 0x00001957, 0x00001956, 0x00001955, 0x00001954, 0x00001953,
	0x00001952, 0x00001951, 0x00001950, 0x0000194F, 0x0000194E, 0x0000194D, 0x0000194C, 0x0000194B,
	0x0000194A, 0x00001949, 0x00001948, 0x00001947, 0x00001946, 0x00001945, 0x00001944, 0x00001943,
	0x00001942, 0x00001941, 0x00001940, 0x0000193F, 0x0000193E, 0x0000193D, 0x0000193C, 0x0000193B,
	0x0000193A, 0x00001939, 0x00001937, 0x00001936, 0x00001935, 0x00001934, 0x00001933, 0x00001932,
	0x00001931, 0x00001930, 0x0000192F, 0x0000192E, 0x0000192D, 0x0000192C, 0x0000192B, 0x0000192A,
	0x00001929, 0x00001928, 0x00001927, 0x00001926, 0x00001925, 0x00001924, 0x00001923, 0x00001922,
	0x00001921, 0x00001920, 0x0000191F, 0x0000191E, 0x0000191D, 0x0000191C, 0x0000191B, 0x0000191A,
	0x00001919, 0x00001918, 0x00001917, 0x00001916, 0x00001915, 0x00001914, 0x00001913, 0x00001912,
	0x00001911, 0x00001910, 0x0000190F, 0x0000190E, 0x0000190D, 0x0000190C, 0x0000190B, 0x0000190A,
	0x00001909, 0x00001908, 0x00001907, 0x00001906, 0x00001905, 0x00001904, 0x00001903, 0x00001902,
	0x00001901, 0x00001900, 0x000018FF, 0x000018FE, 0x000018FD, 0x000018FC, 0x000018FB, 0x000018FA,
	0x000018F9, 0x000018F8, 0x000018F7, 0x000018F6, 0x000018F5, 0x000018F4, 0x000018F3, 0x000018F2,
	0x000018F1, 0x000018F0, 0x000018EF, 0x000018EE, 0x000018ED, 0x000018EC, 0x000018EB, 0x000018EA,
	0x000018E9, 0x000018E8, 0x000018E7, 0x000018E6, 0x000018E5, 0x000018E4, 0x000018E3, 0x000018E2,
	0x000018E1, 0x000018E0, 0x000018DF, 0x000018DE, 0x000018DD, 0x000018DC, 0x000018DB, 0x000018DA,
	0x000018D9, 0x000018D8, 0x000018D7, 0x000018D6, 0x000018D5, 0x000018D4, 0x000018D3, 0x000018D2,
	0x000018D1, 0x000018D0, 0x000018CF, 0x000018CE, 0x000018CD, 0x000018CC, 0x000018CB, 0x000018CA,
	0x000018C9, 0x000018C8, 0x000018C7, 0x000018C6, 0x000018C5, 0x000018C4, 0x000018C3, 0x000018C2,
	0x000018C1, 0x000018C0, 0x000018BF, 0x000018BE, 0x000018BD, 0x000018BC, 0x000018BB, 0x000018BA,
	0x000018B9, 0x000018B8, 0x000018B7, 0x000018B6, 0x000018B5, 0x000018B4, 0x000018B3, 0x000018B2,
	0x000018B1, 0x000018B0, 0x000018AF, 0x000018AE, 0x000018AD, 0x000018AC, 0x000018AB, 0x000018AA,
	0x000018A9, 0x000018A8, 0x000018A7, 0x000018A6, 0x000018A5, 0x000018A4, 0x000018A3, 0x000018A2,
	0x000018A1, 0x000018A0, 0x0000189F, 0x0000189E, 0x0000189D, 0x0000189C, 0x0000189B, 0x0000189A,
	0x00001899, 0x00001898, 0x00001897, 0x00001896, 0x00001895, 0x00001894, 0x00001893, 0x00001892,
	0x00001891, 0x00001890, 0x0000188F, 0x0000188E, 0x0000188D, 0x0000188C, 0x0000188B, 0x0000188A,
	0x00001889, 0x00001888, 0x00001887, 0x00001886, 0x00001885, 0x00001884, 0x00001883, 0x00001882,
	0x00001881, 0x00001880, 0x0000187F, 0x0000187E, 0x0000187D, 0x0000187C, 0x0000187B, 0x0000187A,
	0x00001879, 0x00001878, 0x00001877, 0x00001876, 0x00001875, 0x00001874, 0x00001873, 0x00001872,
	0x00001871, 0x00001870, 0x0000186F, 0x0000186E, 0x0000186D, 0x0000186C, 0x0000186B, 0x0000186A,
	0x00001869, 0x00001868, 0x00001867, 0x00001866, 0x00001865, 0x00001864, 0x00001863, 0x00001862,
	0x00001861, 0x00001860, 0x0000185F, 0x0000185E, 0x0000185D, 0x0000185C, 0x0000185B, 0x0000185A,
	0x00001859, 0x00001858, 0x00001857, 0x00001856, 0x00001855, 0x00001854, 0x00001853, 0x00001852,
	0x00001851, 0x00001850, 0x0000184F, 0x0000184E, 0x0000184D, 0x0000184C, 0x0000184B, 0x0000184A,
	0x00001849, 0x00001848, 0x00001847, 0x00001846, 0x00001845, 0x00001844, 0x00001843, 0x00001842,
	0x00001841, 0x00001840, 0x0000183F, 0x0000183E, 0x0000183D, 0x0000183C, 0x0000183B, 0x0000183A,
	0x00001839, 0x00001838, 0x00001837, 0x00001836, 0x00001835, 0x00001834, 0x00001833, 0x00001832,
	0x00001831, 0x00001830, 0x0000182F, 0x0000182E, 0x0000182D, 0x0000182C, 0x0000182B, 0x0000182A,
	0x00001829, 0x00001828, 0x00001827, 0x00001826, 0x00001825, 0x00001824, 0x00001823, 0x00001822,
	0x00001821, 0x00001820, 0x0000181F, 0x0000181E, 0x0000181D, 0x0000181C, 0x0000181B, 0x0000181A,
	0x00001819, 0x00001818, 0x00001817, 0x00001816, 0x00001815, 0x00001814, 0x00001813, 0x00001812,
	0x00001811, 0x00001810, 0x0000180F, 0x0000180E, 0x0000180D, 0x0000180C, 0x0000180B, 0x0000180A,
	0x00001809, 0x00001808, 0x00001807, 0x00001806, 0x00001805, 0x00001804, 0x00001803, 0x00001802,
	0x00001801, 0x00001800, 0x000017FF, 0x000017FE, 0x000017FD, 0x000017FC, 0x000017FB, 0x000017FA,
	0x000017F9, 0x000017F8, 0x000017F7, 0x000017F6, 0x000017F5, 0x000017F4, 0x000017F3, 0x000017F2,
	0x000017F1, 0x000017F0, 0x000017EF, 0x000017EE, 0x000017ED, 0x000017EC, 0x000017EB, 0x000017EA,
	0x000017E9, 0x000017E8, 0x000017E7, 0x000017E6, 0x000017E5, 0x000017E4, 0x000017E3, 0x000017E2,
	0x000017E1, 0x000017E0, 0x000017DF, 0x000017DE, 0x000017DD, 0x000017DC, 0x000017DB, 0x000017DA,
	0x000017D9, 0x000017D8, 0x000017D7, 0x000017D6, 0x000017D5, 0x000017D4, 0x000017D3, 0x000017D2,
	0x000017D1, 0x000017D0, 0x000017CF, 0x000017CE, 0x000017CD, 0x000017CC, 0x000017CB, 0x000017CA,
	0x000017C9, 0x000017C8, 0x000017C7, 0x000017C6, 0x000017C5, 0x000017C4, 0x000017C3, 0x000017C2,
	0x000017C1, 0x000017C0, 0x000017BF, 0x000017BE, 0x000017BD, 0x000017BC, 0x000017BB, 0x000017BA,
	0x000017B9, 0x000017B8, 0x000017B7, 0x000017B6, 0x000017B5, 0x000017B4, 0x000017B3, 0x000017B2,
	0x000017B1, 0x000017B0, 0x000017AF, 0x000017AE, 0x000017AD, 0x000017AC, 0x000017AB, 0x000017AA,
	0x000017A9, 0x000017A8, 0x000017A7, 0x000017A6, 0x000017A5, 0x000017A4, 0x000017A3, 0x000017A2,
	0x000017A1, 0x000017A0, 0x0000179F, 0x0000179E, 0x0000179D, 0x0000179C, 0x0000179B, 0x0000179A,
	0x00001799, 0x00001798, 0x00001797, 0x00001796, 0x00001795, 0x00001794, 0x00001793, 0x00001792,
	0x00001791, 0x00001790, 0x0000178F, 0x0000178E, 0x0000178D, 0x0000178C, 0x0000178B, 0x0000178A,
	0x00001789, 0x00001788, 0x00001787, 0x00001786, 0x00001785, 0x00001784, 0x00001783, 0x00001782,
	0x00001781, 0x00001780, 0x0000177F, 0x0000177E, 0x0000177D, 0x0000177C, 0x0000177B, 0x0000177A,
	0x00001779, 0x00001778, 0x00001777, 0x00001776, 0x00001775, 0x00001774, 0x00001773, 0x00001772,
	0x00001771, 0x00001770, 0x0000176F, 0x0000176E, 0x0000176D, 0x0000176C, 0x0000176B, 0x0000176A,
	0x00001769, 0x00001768, 0x00001767, 0x00001766, 0x00001765, 0x00001764, 0x00001763, 0x00001762,
	0x00001761, 0x00001760, 0x0000175F, 0x0000175E, 0x0000175D, 0x0000175C, 0x0000175B, 0x0000175A,
	0x00001759, 0x00001758, 0x00001757, 0x00001756, 0x00001755, 0x00001754, 0x00001753, 0x00001752,
	0x00001751, 0x0000174F, 0x0000174E, 0x0000174D, 0x0000174C, 0x0000174B, 0x0000174A, 0x00001749,
	0x00001748, 0x00001747, 0x00001746, 0x00001745, 0x00001744, 0x00001743, 0x00001742, 0x00001741,
	0x00001740, 0x0000173F, 0x0000173E, 0x0000173D, 0x0000173C, 0x0000173B, 0x0000173A, 0x00001739,
	0x00001738, 0x00001737, 0x00001736, 0x00001735, 0x00001734, 0x00001733, 0x00001732, 0x00001731,
	0x00001730, 0x0000172F, 0x0000172E, 0x0000172D, 0x0000172C, 0x0000172B, 0x0000172A, 0x00001729,
	0x00001728, 0x00001727, 0x00001726, 0x00001725, 0x00001724, 0x00001723, 0x00001722, 0x00001721,
	0x00001720, 0x0000171F, 0x0000171E, 0x0000171D, 0x0000171C, 0x0000171B, 0x0000171A, 0x00001719,
	0x00001718, 0x00001717, 0x00001716, 0x00001715, 0x00001714, 0x00001713, 0x00001712, 0x00001711,
	0x00001710, 0x0000170F, 0x0000170E, 0x0000170D, 0x0000170C, 0x0000170B, 0x0000170A, 0x00001709,
	0x00001708, 0x00001707, 0x00001706, 0x00001705, 0x00001704, 0x00001703, 0x00001702, 0x00001701,
	0x00001700, 0x000016FF, 0x000016FE, 0x000016FD, 0x000016FC, 0x000016FB, 0x000016FA, 0x000016F9,
	0x000016F8, 0x000016F7, 0x000016F6, 0x000016F5, 0x000016F4, 0x000016F3, 0x000016F2, 0x000016F1,
	0x000016F0, 0x000016EF, 0x000016EE, 0x000016ED, 0x000016EC, 0x000016EB, 0x000016EA, 0x000016E9,
	0x000016E8, 0x000016E7, 0x000016E6, 0x000016E5, 0x000016E4, 0x000016E3, 0x000016E2, 0x000016E1,
	0x000016E0, 0x000016DF, 0x000016DE, 0x000016DD, 0x000016DC, 0x000016DB, 0x000016DA, 0x000016D9,
	0x000016D8, 0x000016D6, 0x000016D5, 0x000016D4, 0x000016D3, 0x000016D2, 0x000016D1, 0x000016D0,
	0x000016CF, 0x000016CE, 0x000016CD, 0x000016CC, 0x000016CB, 0x000016CA, 0x000016C9, 0x000016C8,
	0x000016C7, 0x000016C6, 0x000016C5, 0x000016C4, 0x000016C3, 0x000016C2, 0x000016C1, 0x000016C0,
	0x000016BF, 0x000016BE, 0x000016BD, 0x000016BC, 0x000016BB, 0x000016BA, 0x000016B9, 0x000016B8,
	0x000016B7, 0x000016B6, 0x000016B5, 0x000016B4, 0x000016B3, 0x000016B2, 0x000016B1, 0x000016B0,
	0x000016AF, 0x000016AE, 0x000016AD, 0x000016AC, 0x000016AB, 0x000016AA, 0x000016A9, 0x000016A8,
	0x000016A7, 0x000016A6, 0x000016A5, 0x000016A4, 0x000016A3, 0x000016A2, 0x000016A1, 0x000016A0,
	0x0000169F, 0x0000169E, 0x0000169D, 0x0000169C, 0x0000169B, 0x0000169A, 0x00001699, 0x00001698,
	0x00001697, 0x00001696, 0x00001695, 0x00001694, 0x00001693, 0x00001692, 0x00001691, 0x00001690,
	0x0000168F, 0x0000168E, 0x0000168D, 0x0000168C, 0x0000168B, 0x0000168A, 0x00001689, 0x00001688,
	0x00001687, 0x00001686, 0x00001685, 0x00001684, 0x00001683, 0x00001681, 0x00001680, 0x0000167F,
	0x0000167E, 0x0000167D, 0x0000167C, 0x0000167B, 0x0000167A, 0x00001679, 0x00001678, 0x00001677,
	0x00001676, 0x00001675, 0x00001674, 0x00001673, 0x00001672, 0x00001671, 0x00001670, 0x0000166F,
	0x0000166E, 0x0000166D, 0x0000166C, 0x0000166B, 0x0000166A, 0x00001669, 0x00001668, 0x00001667,
	0x00001666, 0x00001665, 0x00001664, 0x00001663, 0x00001662, 0x00001661, 0x00001660, 0x0000165F,
	0x0000165E, 0x0000165D, 0x0000165C, 0x0000165B, 0x0000165A, 0x00001659, 0x00001658, 0x00001657,
	0x00001656, 0x00001655, 0x00001654, 0x00001653, 0x00001652, 0x00001651, 0x00001650, 0x0000164F,
	0x0000164E, 0x0000164D, 0x0000164C, 0x0000164B, 0x0000164A, 0x00001649, 0x00001648, 0x00001647,
	0x00001646, 0x00001645, 0x00001644, 0x00001643, 0x00001642, 0x00001641, 0x00001640, 0x0000163E,
	0x0000163D, 0x0000163C, 0x0000163B, 0x0000163A, 0x00001639, 0x00001638, 0x00001637, 0x00001636,
	0x00001635, 0x00001634, 0x00001633, 0x00001632, 0x00001631, 0x00001630, 0x0000162F, 0x0000162E,
	0x0000162D, 0x0000162C, 0x0000162B, 0x0000162A, 0x00001629, 0x00001628, 0x00001627, 0x00001626,
	0x00001625, 0x00001624, 0x00001623, 0x00001622, 0x00001621, 0x00001620, 0x0000161F, 0x0000161E,
	0x0000161D, 0x0000161C, 0x0000161B, 0x0000161A, 0x00001619, 0x00001618, 0x00001617, 0x00001616,
	0x00001615, 0x00001614, 0x00001613, 0x00001612, 0x00001611, 0x00001610, 0x0000160F, 0x0000160E,
	0x0000160D, 0x0000160C, 0x0000160B, 0x0000160A, 0x00001609, 0x00001608, 0x00001607, 0x00001605,
	0x00001604, 0x00001603, 0x00001602, 0x00001601, 0x00001600, 0x000015FF, 0x000015FE, 0x000015FD,
	0x000015FC, 0x000015FB, 0x000015FA, 0x000015F9, 0x000015F8, 0x000015F7, 0x000015F6, 0x000015F5,
	0x000015F4, 0x000015F3, 0x000015F2, 0x000015F1, 0x000015F0, 0x000015EF, 0x000015EE, 0x000015ED,
	0x000015EC, 0x000015EB, 0x000015EA, 0x000015E9, 0x000015E8, 0x000015E7, 0x000015E6, 0x000015E5,
	0x000015E4, 0x000015E3, 0x000015E2, 0x000015E1, 0x000015E0, 0x000015DF, 0x000015DE, 0x000015DD,
	0x000015DC, 0x000015DB, 0x000015DA, 0x000015D9, 0x000015D8, 0x000015D7, 0x000015D6, 0x000015D5,
	0x000015D3, 0x000015D2, 0x000015D1, 0x000015D0, 0x000015CF, 0x000015CE, 0x000015CD, 0x000015CC,
	0x000015CB, 0x000015CA, 0x000015C9, 0x000015C8, 0x000015C7, 0x000015C6, 0x000015C5, 0x000015C4,
	0x000015C3, 0x000015C2, 0x000015C1, 0x000015C0, 0x000015BF, 0x000015BE, 0x000015BD, 0x000015BC,
	0x000015BB, 0x000015BA, 0x000015B9, 0x000015B8, 0x000015B7, 0x000015B6, 0x000015B5, 0x000015B4,
	0x000015B3, 0x000015B2, 0x000015B1, 0x000015B0, 0x000015AF, 0x000015AE, 0x000015AD, 0x000015AC,
	0x000015AB, 0x000015AA, 0x000015A9, 0x000015A8, 0x000015A6, 0x000015A5, 0x000015A4, 0x000015A3,
	0x000015A2, 0x000015A1, 0x000015A0, 0x0000159F, 0x0000159E, 0x0000159D, 0x0000159C, 0x0000159B,
	0x0000159A, 0x00001599, 0x00001598, 0x00001597, 0x00001596, 0x00001595, 0x00001594, 0x00001593,
	0x00001592, 0x00001591, 0x00001590, 0x0000158F, 0x0000158E, 0x0000158D, 0x0000158C, 0x0000158B,
	0x0000158A, 0x00001589, 0x00001588, 0x00001587, 0x00001586, 0x00001585, 0x00001584, 0x00001583,
	0x00001582, 0x00001581, 0x00001580, 0x0000157F, 0x0000157D, 0x0000157C, 0x0000157B, 0x0000157A,
	0x00001579, 0x00001578, 0x00001577, 0x00001576, 0x00001575, 0x00001574, 0x00001573, 0x00001572,
	0x00001571, 0x00001570, 0x0000156F, 0x0000156E, 0x0000156D, 0x0000156C, 0x0000156B, 0x0000156A,
	0x00001569, 0x00001568, 0x00001567, 0x00001566, 0x00001565, 0x00001564, 0x00001563, 0x00001562,
	0x00001561, 0x00001560, 0x0000155F, 0x0000155E, 0x0000155D, 0x0000155C, 0x0000155B, 0x0000155A,
	0x00001558, 0x00001557, 0x00001556, 0x00001555, 0x00001554, 0x00001553, 0x00001552, 0x00001551,
	0x00001550, 0x0000154F, 0x0000154E, 0x0000154D, 0x0000154C, 0x0000154B, 0x0000154A, 0x00001549,
	0x00001548, 0x00001547, 0x00001546, 0x00001545, 0x00001544, 0x00001543, 0x00001542, 0x00001541,
	0x00001540, 0x0000153F, 0x0000153E, 0x0000153D, 0x0000153C, 0x0000153B, 0x0000153A, 0x00001539,
	0x00001538, 0x00001537, 0x00001535, 0x00001534, 0x00001533, 0x00001532, 0x00001531, 0x00001530,
	0x0000152F, 0x0000152E, 0x0000152D, 0x0000152C, 0x0000152B, 0x0000152A, 0x00001529, 0x00001528,
	0x00001527, 0x00001526, 0x00001525, 0x00001524, 0x00001523, 0x00001522, 0x00001521, 0x00001520,
	0x0000151F, 0x0000151E, 0x0000151D, 0x0000151C, 0x0000151B, 0x0000151A, 0x00001519, 0x00001518,
	0x00001517, 0x00001515, 0x00001514, 0x00001513, 0x00001512, 0x00001511, 0x00001510, 0x0000150F,
	0x0000150E, 0x0000150D, 0x0000150C, 0x0000150B, 0x0000150A, 0x00001509, 0x00001508, 0x00001507,
	0x00001506, 0x00001505, 0x00001504, 0x00001503, 0x00001502, 0x00001501, 0x00001500, 0x000014FF,
	0x000014FE, 0x000014FD, 0x000014FC, 0x000014FB, 0x000014FA, 0x000014F9, 0x000014F8, 0x000014F6,
	0x000014F5, 0x000014F4, 0x000014F3, 0x000014F2, 0x000014F1, 0x000014F0, 0x000014EF, 0x000014EE,
	0x000014ED, 0x000014EC, 0x000014EB, 0x000014EA, 0x000014E9, 0x000014E8, 0x000014E7, 0x000014E6,
	0x000014E5, 0x000014E4, 0x000014E3, 0x000014E2, 0x000014E1, 0x000014E0, 0x000014DF, 0x000014DE,
	0x000014DD, 0x000014DC, 0x000014DB, 0x000014D9, 0x000014D8, 0x000014D7, 0x000014D6, 0x000014D5,
	0x000014D4, 0x000014D3, 0x000014D2, 0x000014D1, 0x000014D0, 0x000014CF, 0x000014CE, 0x000014CD,
	0x000014CC, 0x000014CB, 0x000014CA, 0x000014C9, 0x000014C8, 0x000014C7, 0x000014C6, 0x000014C5,
	0x000014C4, 0x000014C3, 0x000014C2, 0x000014C1, 0x000014C0, 0x000014BE, 0x000014BD, 0x000014BC,
	0x000014BB, 0x000014BA, 0x000014B9, 0x000014B8, 0x000014B7, 0x000014B6, 0x000014B5, 0x000014B4,
	0x000014B3, 0x000014B2, 0x000014B1, 0x000014B0, 0x000014AF, 0x000014AE, 0x000014AD, 0x000014AC,
	0x000014AB, 0x000014AA, 0x000014A9, 0x000014A8, 0x000014A7, 0x000014A6, 0x000014A4, 0x000014A3,
	0x000014A2, 0x000014A1, 0x000014A0, 0x0000149F, 0x0000149E, 0x0000149D, 0x0000149C, 0x0000149B,
	0x0000149A, 0x00001499, 0x00001498, 0x00001497, 0x00001496, 0x00001495, 0x00001494, 0x00001493,
	0x00001492, 0x00001491, 0x00001490, 0x0000148F, 0x0000148E, 0x0000148D, 0x0000148B, 0x0000148A,
	0x00001489, 0x00001488, 0x00001487, 0x00001486, 0x00001485, 0x00001484, 0x00001483, 0x00001482,
	0x00001481, 0x00001480, 0x0000147F, 0x0000147E, 0x0000147D, 0x0000147C, 0x0000147B, 0x0000147A,
	0x00001479, 0x00001478, 0x00001477, 0x00001476, 0x00001475, 0x00001473, 0x00001472, 0x00001471,
	0x00001470, 0x0000146F, 0x0000146E, 0x0000146D, 0x0000146C, 0x0000146B, 0x0000146A, 0x00001469,
	0x00001468, 0x00001467, 0x00001466, 0x00001465, 0x00001464, 0x00001463, 0x00001462, 0x00001461,
	0x00001460, 0x0000145F, 0x0000145E, 0x0000145C, 0x0000145B, 0x0000145A, 0x00001459, 0x00001458,
	0x00001457, 0x00001456, 0x00001455, 0x00001454, 0x00001453, 0x00001452, 0x00001451, 0x00001450,
	0x0000144F, 0x0000144E, 0x0000144D, 0x0000144C, 0x0000144B, 0x0000144A, 0x00001449, 0x00001448,
	0x00001447, 0x00001445, 0x00001444, 0x00001443, 0x00001442, 0x00001441, 0x00001440, 0x0000143F,
	0x0000143E, 0x0000143D, 0x0000143C, 0x0000143B, 0x0000143A, 0x00001439, 0x00001438, 0x00001437,
	0x00001436, 0x00001435, 0x00001434, 0x00001433, 0x00001432, 0x00001430, 0x0000142F, 0x0000142E,
	0x0000142D, 0x0000142C, 0x0000142B, 0x0000142A, 0x00001429, 0x00001428, 0x00001427, 0x00001426,
	0x00001425, 0x00001424, 0x00001423, 0x00001422, 0x00001421, 0x00001420, 0x0000141F, 0x0000141E,
	0x0000141D, 0x0000141B, 0x0000141A, 0x00001419, 0x00001418, 0x00001417, 0x00001416, 0x00001415,
	0x00001414, 0x00001413, 0x00001412, 0x00001411, 0x00001410, 0x0000140F, 0x0000140E, 0x0000140D,
	0x0000140C, 0x0000140B, 0x0000140A, 0x00001409, 0x00001407, 0x00001406, 0x00001405, 0x00001404,
	0x00001403, 0x00001402, 0x00001401, 0x00001400, 0x000013FF, 0x000013FE, 0x000013FD, 0x000013FC,
	0x000013FB, 0x000013FA, 0x000013F9, 0x000013F8, 0x000013F7, 0x000013F6, 0x000013F5, 0x000013F3,
	0x000013F2, 0x000013F1, 0x000013F0, 0x000013EF, 0x000013EE, 0x000013ED, 0x000013EC, 0x000013EB,
	0x000013EA, 0x000013E9, 0x000013E8, 0x000013E7, 0x000013E6, 0x000013E5, 0x000013E4, 0x000013E3,
	0x000013E2, 0x000013E0, 0x000013DF, 0x000013DE, 0x000013DD, 0x000013DC, 0x000013DB, 0x000013DA,
	0x000013D9, 0x000013D8, 0x000013D7, 0x000013D6, 0x000013D5, 0x000013D4, 0x000013D3, 0x000013D2,
	0x000013D1, 0x000013D0, 0x000013CE, 0x000013CD, 0x000013CC, 0x000013CB, 0x000013CA, 0x000013C9,
	0x000013C8, 0x000013C7, 0x000013C6, 0x000013C5, 0x000013C4, 0x000013C3, 0x000013C2, 0x000013C1,
	0x000013C0, 0x000013BF, 0x000013BE, 0x000013BC, 0x000013BB, 0x000013BA, 0x000013B9, 0x000013B8,
	0x000013B7, 0x000013B6, 0x000013B5, 0x000013B4, 0x000013B3, 0x000013B2, 0x000013B1, 0x000013B0,
	0x000013AF, 0x000013AE, 0x000013AD, 0x000013AC, 0x000013AA, 0x000013A9, 0x000013A8, 0x000013A7,
	0x000013A6, 0x000013A5, 0x000013A4, 0x000013A3, 0x000013A2, 0x000013A1, 0x000013A0, 0x0000139F,
	0x0000139E, 0x0000139D, 0x0000139C, 0x0000139B, 0x00001399, 0x00001398, 0x00001397, 0x00001396,
	0x00001395, 0x00001394, 0x00001393, 0x00001392, 0x00001391, 0x00001390, 0x0000138F, 0x0000138E,
	0x0000138D, 0x0000138C, 0x0000138B, 0x0000138A, 0x00001388, 0x00001387, 0x00001386, 0x00001385,
	0x00001384, 0x00001383, 0x00001382, 0x00001381, 0x00001380, 0x0000137F, 0x0000137E, 0x0000137D,
	0x0000137C, 0x0000137B, 0x0000137A, 0x00001378, 0x00001377, 0x00001376, 0x00001375, 0x00001374,
	0x00001373, 0x00001372, 0x00001371, 0x00001370, 0x0000136F, 0x0000136E, 0x0000136D, 0x0000136C,
	0x0000136B, 0x0000136A, 0x00001368, 0x00001367, 0x00001366, 0x00001365, 0x00001364, 0x00001363,
	0x00001362, 0x00001361, 0x00001360, 0x0000135F, 0x0000135E, 0x0000135D, 0x0000135C, 0x0000135B,
	0x0000135A, 0x00001358, 0x00001357, 0x00001356, 0x00001355, 0x00001354, 0x00001353, 0x00001352,
	0x00001351, 0x00001350, 0x0000134F, 0x0000134E, 0x0000134D, 0x0000134C, 0x0000134B, 0x00001349,
	0x00001348, 0x00001347, 0x00001346, 0x00001345, 0x00001344, 0x00001343, 0x00001342, 0x00001341,
	0x00001340, 0x0000133F, 0x0000133E, 0x0000133D, 0x0000133C, 0x0000133A, 0x00001339, 0x00001338,
	0x00001337, 0x00001336, 0x00001335, 0x00001334, 0x00001333, 0x00001332, 0x00001331, 0x00001330,
	0x0000132F, 0x0000132E, 0x0000132D, 0x0000132B, 0x0000132A, 0x00001329, 0x00001328, 0x00001327,
	0x00001326, 0x00001325, 0x00001324, 0x00001323, 0x00001322, 0x00001321, 0x00001320, 0x0000131F,
	0x0000131D, 0x0000131C, 0x0000131B, 0x0000131A, 0x00001319, 0x00001318, 0x00001317, 0x00001316,
	0x00001315, 0x00001314, 0x00001313, 0x00001312, 0x00001311, 0x0000130F, 0x0000130E, 0x0000130D,
	0x0000130C, 0x0000130B, 0x0000130A, 0x00001309, 0x00001308, 0x00001307, 0x00001306, 0x00001305,
	0x00001304, 0x00001303, 0x00001301, 0x00001300, 0x000012FF, 0x000012FE, 0x000012FD, 0x000012FC,
	0x000012FB, 0x000012FA, 0x000012F9, 0x000012F8, 0x000012F7, 0x000012F6, 0x000012F5, 0x000012F3,
	0x000012F2, 0x000012F1, 0x000012F0, 0x000012EF, 0x000012EE, 0x000012ED, 0x000012EC, 0x000012EB,
	0x000012EA, 0x000012E9, 0x000012E8, 0x000012E6, 0x000012E5, 0x000012E4, 0x000012E3, 0x000012E2,
	0x000012E1, 0x000012E0, 0x000012DF, 0x000012DE, 0x000012DD, 0x000012DC, 0x000012DB, 0x000012DA,
	0x000012D8, 0x000012D7, 0x000012D6, 0x000012D5, 0x000012D4, 0x000012D3, 0x000012D2, 0x000012D1,
	0x000012D0, 0x000012CF, 0x000012CE, 0x000012CD, 0x000012CB, 0x000012CA, 0x000012C9, 0x000012C8,
	0x000012C7, 0x000012C6, 0x000012C5, 0x000012C4, 0x000012C3, 0x000012C2, 0x000012C1, 0x000012BF,
	0x000012BE, 0x000012BD, 0x000012BC, 0x000012BB, 0x000012BA, 0x000012B9, 0x000012B8, 0x000012B7,
	0x000012B6, 0x000012B5, 0x000012B4, 0x000012B2, 0x000012B1, 0x000012B0, 0x000012AF, 0x000012AE,
	0x000012AD, 0x000012AC, 0x000012AB, 0x000012AA, 0x000012A9, 0x000012A8, 0x000012A7, 0x000012A5,
	0x000012A4, 0x000012A3, 0x000012A2, 0x000012A1, 0x000012A0, 0x0000129F, 0x0000129E, 0x0000129D,
	0x0000129C, 0x0000129B, 0x00001299, 0x00001298, 0x00001297, 0x00001296, 0x00001295, 0x00001294,
	0x00001293, 0x00001292, 0x00001291, 0x00001290, 0x0000128F, 0x0000128D, 0x0000128C, 0x0000128B,
	0x0000128A, 0x00001289, 0x00001288, 0x00001287, 0x00001286, 0x00001285, 0x00001284, 0x00001283,
	0x00001281, 0x00001280, 0x0000127F, 0x0000127E, 0x0000127D, 0x0000127C, 0x0000127B, 0x0000127A,
	0x00001279, 0x00001278, 0x00001277, 0x00001275, 0x00001274, 0x00001273, 0x00001272, 0x00001271,
	0x00001270, 0x0000126F, 0x0000126E, 0x0000126D, 0x0000126C, 0x0000126A, 0x00001269, 0x00001268,
	0x00001267, 0x00001266, 0x00001265, 0x00001264, 0x00001263, 0x00001262, 0x00001261, 0x00001260,
	0x0000125E, 0x0000125D, 0x0000125C, 0x0000125B, 0x0000125A, 0x00001259, 0x00001258, 0x00001257,
	0x00001256, 0x00001255, 0x00001253, 0x00001252, 0x00001251, 0x00001250, 0x0000124F, 0x0000124E,
	0x0000124D, 0x0000124C, 0x0000124B, 0x0000124A, 0x00001248, 0x00001247, 0x00001246, 0x00001245,
	0x00001244, 0x00001243, 0x00001242, 0x00001241, 0x00001240, 0x0000123F, 0x0000123D, 0x0000123C,
	0x0000123B, 0x0000123A, 0x00001239, 0x00001238, 0x00001237, 0x00001236, 0x00001235, 0x00001234,
	0x00001232, 0x00001231, 0x00001230, 0x0000122F, 0x0000122E, 0x0000122D, 0x0000122C, 0x0000122B,
	0x0000122A, 0x00001229, 0x00001227, 0x00001226, 0x00001225, 0x00001224, 0x00001223, 0x00001222,
	0x00001221, 0x00001220, 0x0000121F, 0x0000121D, 0x0000121C, 0x0000121B, 0x0000121A, 0x00001219,
	0x00001218, 0x00001217, 0x00001216, 0x00001215, 0x00001214, 0x00001212, 0x00001211, 0x00001210,
	0x0000120F, 0x0000120E, 0x0000120D, 0x0000120C, 0x0000120B, 0x0000120A, 0x00001208, 0x00001207,
	0x00001206, 0x00001205, 0x00001204, 0x00001203, 0x00001202, 0x00001201, 0x00001200, 0x000011FF,
	0x000011FD, 0x000011FC, 0x000011FB, 0x000011FA, 0x000011F9, 0x000011F8, 0x000011F7, 0x000011F6,
	0x000011F5, 0x000011F3, 0x000011F2, 0x000011F1, 0x000011F0, 0x000011EF, 0x000011EE, 0x000011ED,
	0x000011EC, 0x000011EB, 0x000011E9, 0x000011E8, 0x000011E7, 0x000011E6, 0x000011E5, 0x000011E4,
	0x000011E3, 0x000011E2, 0x000011E1, 0x000011DF, 0x000011DE, 0x000011DD, 0x000011DC, 0x000011DB,
	0x000011DA, 0x000011D9, 0x000011D8, 0x000011D7, 0x000011D5, 0x000011D4, 0x000011D3, 0x000011D2,
	0x000011D1, 0x000011D0, 0x000011CF, 0x000011CE, 0x000011CC, 0x000011CB, 0x000011CA, 0x000011C9,
	0x000011C8, 0x000011C7, 0x000011C6, 0x000011C5, 0x000011C4, 0x000011C2, 0x000011C1, 0x000011C0,
	0x000011BF, 0x000011BE, 0x000011BD, 0x000011BC, 0x000011BB, 0x000011BA, 0x000011B8, 0x000011B7,
	0x000011B6, 0x000011B5, 0x000011B4, 0x000011B3, 0x000011B2, 0x000011B1, 0x000011AF, 0x000011AE,
	0x000011AD, 0x000011AC, 0x000011AB, 0x000011AA, 0x000011A9, 0x000011A8, 0x000011A6, 0x000011A5,
	0x000011A4, 0x000011A3, 0x000011A2, 0x000011A1, 0x000011A0, 0x0000119F, 0x0000119E, 0x0000119C,
	0x0000119B, 0x0000119A, 0x00001199, 0x00001198, 0x00001197, 0x00001196, 0x00001195, 0x00001193,
	0x00001192, 0x00001191, 0x00001190, 0x0000118F, 0x0000118E, 0x0000118D, 0x0000118C, 0x0000118A,
	0x00001189, 0x00001188, 0x00001187, 0x00001186, 0x00001185, 0x00001184, 0x00001183, 0x00001181,
	0x00001180, 0x0000117F, 0x0000117E, 0x0000117D, 0x0000117C, 0x0000117B, 0x0000117A, 0x00001178,
	0x00001177, 0x00001176, 0x00001175, 0x00001174, 0x00001173, 0x00001172, 0x00001171, 0x0000116F,
	0x0000116E, 0x0000116D, 0x0000116C, 0x0000116B, 0x0000116A, 0x00001169, 0x00001168, 0x00001166,
	0x00001165, 0x00001164, 0x00001163, 0x00001162, 0x00001161, 0x00001160, 0x0000115E, 0x0000115D,
	0x0000115C, 0x0000115B, 0x0000115A, 0x00001159, 0x00001158, 0x00001157, 0x00001155, 0x00001154,
	0x00001153, 0x00001152, 0x00001151, 0x00001150, 0x0000114F, 0x0000114D, 0x0000114C, 0x0000114B,
	0x0000114A, 0x00001149, 0x00001148, 0x00001147, 0x00001146, 0x00001144, 0x00001143, 0x00001142,
	0x00001141, 0x00001140, 0x0000113F, 0x0000113E, 0x0000113C, 0x0000113B, 0x0000113A, 0x00001139,
	0x00001138, 0x00001137, 0x00001136, 0x00001135, 0x00001133, 0x00001132, 0x00001131, 0x00001130,
	0x0000112F, 0x0000112E, 0x0000112D, 0x0000112B, 0x0000112A, 0x00001129, 0x00001128, 0x00001127,
	0x00001126, 0x00001125, 0x00001123, 0x00001122, 0x00001121, 0x00001120, 0x0000111F, 0x0000111E,
	0x0000111D, 0x0000111B, 0x0000111A, 0x00001119, 0x00001118, 0x00001117, 0x00001116, 0x00001115,
	0x00001113, 0x00001112, 0x00001111, 0x00001110, 0x0000110F, 0x0000110E, 0x0000110D, 0x0000110C,
	0x0000110A, 0x00001109, 0x00001108, 0x00001107, 0x00001106, 0x00001105, 0x00001103, 0x00001102,
	0x00001101, 0x00001100, 0x000010FF, 0x000010FE, 0x000010FD, 0x000010FB, 0x000010FA, 0x000010F9,
	0x000010F8, 0x000010F7, 0x000010F6, 0x000010F5, 0x000010F3, 0x000010F2, 0x000010F1, 0x000010F0,
	0x000010EF, 0x000010EE, 0x000010ED, 0x000010EB, 0x000010EA, 0x000010E9, 0x000010E8, 0x000010E7,
	0x000010E6, 0x000010E5, 0x000010E3, 0x000010E2, 0x000010E1, 0x000010E0, 0x000010DF, 0x000010DE,
	0x000010DC, 0x000010DB, 0x000010DA, 0x000010D9, 0x000010D8, 0x000010D7, 0x000010D6, 0x000010D4,
	0x000010D3, 0x000010D2, 0x000010D1, 0x000010D0, 0x000010CF, 0x000010CE, 0x000010CC, 0x000010CB,
	0x000010CA, 0x000010C9, 0x000010C8, 0x000010C7, 0x000010C5, 0x000010C4, 0x000010C3, 0x000010C2,
	0x000010C1, 0x000010C0, 0x000010BF, 0x000010BD, 0x000010BC, 0x000010BB, 0x000010BA, 0x000010B9,
	0x000010B8, 0x000010B6, 0x000010B5, 0x000010B4, 0x000010B3, 0x000010B2, 0x000010B1, 0x000010AF,
	0x000010AE, 0x000010AD, 0x000010AC, 0x000010AB, 0x000010AA, 0x000010A9, 0x000010A7, 0x000010A6,
	0x000010A5, 0x000010A4, 0x000010A3, 0x000010A2, 0x000010A0, 0x0000109F, 0x0000109E, 0x0000109D,
	0x0000109C, 0x0000109B, 0x00001099, 0x00001098, 0x00001097, 0x00001096, 0x00001095, 0x00001094,
	0x00001092, 0x00001091, 0x00001090, 0x0000108F, 0x0000108E, 0x0000108D, 0x0000108C, 0x0000108A,
	0x00001089, 0x00001088, 0x00001087, 0x00001086, 0x00001085, 0x00001083, 0x00001082, 0x00001081,
	0x00001080, 0x0000107F, 0x0000107E, 0x0000107C, 0x0000107B, 0x0000107A, 0x00001079, 0x00001078,
	0x00001077, 0x00001075, 0x00001074, 0x00001073, 0x00001072, 0x00001071, 0x00001070, 0x0000106E,
	0x0000106D, 0x0000106C, 0x0000106B, 0x0000106A, 0x00001069, 0x00001067, 0x00001066, 0x00001065,
	0x00001064, 0x00001063, 0x00001061, 0x00001060, 0x0000105F, 0x0000105E, 0x0000105D, 0x0000105C,
	0x0000105A, 0x00001059, 0x00001058, 0x00001057, 0x00001056, 0x00001055, 0x00001053, 0x00001052,
	0x00001051, 0x00001050, 0x0000104F, 0x0000104E, 0x0000104C, 0x0000104B, 0x0000104A, 0x00001049,
	0x00001048, 0x00001047, 0x00001045, 0x00001044, 0x00001043, 0x00001042, 0x00001041, 0x0000103F,
	0x0000103E, 0x0000103D, 0x0000103C, 0x0000103B, 0x0000103A, 0x00001038, 0x00001037, 0x00001036,
	0x00001035, 0x00001034, 0x00001033, 0x00001031, 0x00001030, 0x0000102F, 0x0000102E, 0x0000102D,
	0x0000102B, 0x0000102A, 0x00001029, 0x00001028, 0x00001027, 0x00001026, 0x00001024, 0x00001023,
	0x00001022, 0x00001021, 0x00001020, 0x0000101E, 0x0000101D, 0x0000101C, 0x0000101B, 0x0000101A,
	0x00001019, 0x00001017, 0x00001016, 0x00001015, 0x00001014, 0x00001013, 0x00001011, 0x00001010,
	0x0000100F, 0x0000100E, 0x0000100D, 0x0000100B, 0x0000100A, 0x00001009, 0x00001008, 0x00001007,
	0x00001006, 0x00001004, 0x00001003, 0x00001002, 0x00001001, 0x00001000, 0x00000FFE, 0x00000FFD,
	0x00000FFC, 0x00000FFB, 0x00000FFA, 0x00000FF8, 0x00000FF7, 0x00000FF6, 0x00000FF5, 0x00000FF4,
	0x00000FF3, 0x00000FF1, 0x00000FF0, 0x00000FEF, 0x00000FEE, 0x00000FED, 0x00000FEB, 0x00000FEA,
	0x00000FE9, 0x00000FE8, 0x00000FE7, 0x00000FE5, 0x00000FE4, 0x00000FE3, 0x00000FE2, 0x00000FE1,
	0x00000FDF, 0x00000FDE, 0x00000FDD, 0x00000FDC, 0x00000FDB, 0x00000FD9, 0x00000FD8, 0x00000FD7,
	0x00000FD6, 0x00000FD5, 0x00000FD3, 0x00000FD2, 0x00000FD1, 0x00000FD0, 0x00000FCF, 0x00000FCD,
	0x00000FCC, 0x00000FCB, 0x00000FCA, 0x00000FC9, 0x00000FC8, 0x00000FC6, 0x00000FC5, 0x00000FC4,
	0x00000FC3, 0x00000FC2, 0x00000FC0, 0x00000FBF, 0x00000FBE, 0x00000FBD, 0x00000FBC, 0x00000FBA,
	0x00000FB9, 0x00000FB8, 0x00000FB7, 0x00000FB5, 0x00000FB4, 0x00000FB3, 0x00000FB2, 0x00000FB1,
	0x00000FAF, 0x00000FAE, 0x00000FAD, 0x00000FAC, 0x00000FAB, 0x00000FA9, 0x00000FA8, 0x00000FA7,
	0x00000FA6, 0x00000FA5, 0x00000FA3, 0x00000FA2, 0x00000FA1, 0x00000FA0, 0x00000F9F, 0x00000F9D,
	0x00000F9C, 0x00000F9B, 0x00000F9A, 0x00000F99, 0x00000F97, 0x00000F96, 0x00000F95, 0x00000F94,
	0x00000F93, 0x00000F91, 0x00000F90, 0x00000F8F, 0x00000F8E, 0x00000F8C, 0x00000F8B, 0x00000F8A,
	0x00000F89, 0x00000F88, 0x00000F86, 0x00000F85, 0x00000F84, 0x00000F83, 0x00000F82, 0x00000F80,
	0x00000F7F, 0x00000F7E, 0x00000F7D, 0x00000F7B, 0x00000F7A, 0x00000F79, 0x00000F78, 0x00000F77,
	0x00000F75, 0x00000F74, 0x00000F73, 0x00000F72, 0x00000F71, 0x00000F6F, 0x00000F6E, 0x00000F6D,
	0x00000F6C, 0x00000F6A, 0x00000F69, 0x00000F68, 0x00000F67, 0x00000F66, 0x00000F64, 0x00000F63,
	0x00000F62, 0x00000F61, 0x00000F60, 0x00000F5E, 0x00000F5D, 0x00000F5C, 0x00000F5B, 0x00000F59,
	0x00000F58, 0x00000F57, 0x00000F56, 0x00000F55, 0x00000F53, 0x00000F52, 0x00000F51, 0x00000F50,
	0x00000F4E, 0x00000F4D, 0x00000F4C, 0x00000F4B, 0x00000F4A, 0x00000F48, 0x00000F47, 0x00000F46,
	0x00000F45, 0x00000F43, 0x00000F42, 0x00000F41, 0x00000F40, 0x00000F3E, 0x00000F3D, 0x00000F3C,
	0x00000F3B, 0x00000F3A, 0x00000F38, 0x00000F37, 0x00000F36, 0x00000F35, 0x00000F33, 0x00000F32,
	0x00000F31, 0x00000F30, 0x00000F2E, 0x00000F2D, 0x00000F2C, 0x00000F2B, 0x00000F2A, 0x00000F28,
	0x00000F27, 0x00000F26, 0x00000F25, 0x00000F23, 0x00000F22, 0x00000F21, 0x00000F20, 0x00000F1E,
	0x00000F1D, 0x00000F1C, 0x00000F1B, 0x00000F1A, 0x00000F18, 0x00000F17, 0x00000F16, 0x00000F15,
	0x00000F13, 0x00000F12, 0x00000F11, 0x00000F10, 0x00000F0E, 0x00000F0D, 0x00000F0C, 0x00000F0B,
	0x00000F09, 0x00000F08, 0x00000F07, 0x00000F06, 0x00000F05, 0x00000F03, 0x00000F02, 0x00000F01,
	0x00000F00, 0x00000EFE, 0x00000EFD, 0x00000EFC, 0x00000EFB, 0x00000EF9, 0x00000EF8, 0x00000EF7,
	0x00000EF6, 0x00000EF4, 0x00000EF3, 0x00000EF2, 0x00000EF1, 0x00000EEF, 0x00000EEE, 0x00000EED,
	0x00000EEC, 0x00000EEA, 0x00000EE9, 0x00000EE8, 0x00000EE7, 0x00000EE5, 0x00000EE4, 0x00000EE3,
	0x00000EE2, 0x00000EE0, 0x00000EDF, 0x00000EDE, 0x00000EDD, 0x00000EDB, 0x00000EDA, 0x00000ED9,
	0x00000ED8, 0x00000ED6, 0x00000ED5, 0x00000ED4, 0x00000ED3, 0x00000ED1, 0x00000ED0, 0x00000ECF,
	0x00000ECE, 0x00000ECC, 0x00000ECB, 0x00000ECA, 0x00000EC9, 0x00000EC7, 0x00000EC6, 0x00000EC5,
	0x00000EC4, 0x00000EC2, 0x00000EC1, 0x00000EC0, 0x00000EBF, 0x00000EBD, 0x00000EBC, 0x00000EBB,
	0x00000EBA, 0x00000EB8, 0x00000EB7, 0x00000EB6, 0x00000EB5, 0x00000EB3, 0x00000EB2, 0x00000EB1,
	0x00000EB0, 0x00000EAE, 0x00000EAD, 0x00000EAC, 0x00000EAB, 0x00000EA9, 0x00000EA8, 0x00000EA7,
	0x00000EA5, 0x00000EA4, 0x00000EA3, 0x00000EA2, 0x00000EA0, 0x00000E9F, 0x00000E9E, 0x00000E9D,
	0x00000E9B, 0x00000E9A, 0x00000E99, 0x00000E98, 0x00000E96, 0x00000E95, 0x00000E94, 0x00000E93,
	0x00000E91, 0x00000E90, 0x00000E8F, 0x00000E8D, 0x00000E8C, 0x00000E8B, 0x00000E8A, 0x00000E88,
	0x00000E87, 0x00000E86, 0x00000E85, 0x00000E83, 0x00000E82, 0x00000E81, 0x00000E80, 0x00000E7E,
	0x00000E7D, 0x00000E7C, 0x00000E7A, 0x00000E79, 0x00000E78, 0x00000E77, 0x00000E75, 0x00000E74,
	0x00000E73, 0x00000E72, 0x00000E70, 0x00000E6F, 0x00000E6E, 0x00000E6C, 0x00000E6B, 0x00000E6A,
	0x00000E69, 0x00000E67, 0x00000E66, 0x00000E65, 0x00000E64, 0x00000E62, 0x00000E61, 0x00000E60,
	0x00000E5E, 0x00000E5D, 0x00000E5C, 0x00000E5B, 0x00000E59, 0x00000E58, 0x00000E57, 0x00000E55,
	0x00000E54, 0x00000E53, 0x00000E52, 0x00000E50, 0x00000E4F, 0x00000E4E, 0x00000E4C, 0x00000E4B,
	0x00000E4A, 0x00000E49, 0x00000E47, 0x00000E46, 0x00000E45, 0x00000E43, 0x00000E42, 0x00000E41,
	0x00000E40, 0x00000E3E, 0x00000E3D, 0x00000E3C, 0x00000E3A, 0x00000E39, 0x00000E38, 0x00000E37,
	0x00000E35, 0x00000E34, 0x00000E33, 0x00000E31, 0x00000E30, 0x00000E2F, 0x00000E2E, 0x00000E2C,
	0x00000E2B, 0x00000E2A, 0x00000E28, 0x00000E27, 0x00000E26, 0x00000E25, 0x00000E23, 0x00000E22,
	0x00000E21, 0x00000E1F, 0x00000E1E, 0x00000E1D, 0x00000E1B, 0x00000E1A, 0x00000E19, 0x00000E18,
	0x00000E16, 0x00000E15, 0x00000E14, 0x00000E12, 0x00000E11, 0x00000E10, 0x00000E0F, 0x00000E0D,
	0x00000E0C, 0x00000E0B, 0x00000E09, 0x00000E08, 0x00000E07, 0x00000E05, 0x00000E04, 0x00000E03,
	0x00000E01, 0x00000E00, 0x00000DFF, 0x00000DFE, 0x00000DFC, 0x00000DFB, 0x00000DFA, 0x00000DF8,
	0x00000DF7, 0x00000DF6, 0x00000DF4, 0x00000DF3, 0x00000DF2, 0x00000DF1, 0x00000DEF, 0x00000DEE,
	0x00000DED, 0x00000DEB, 0x00000DEA, 0x00000DE9, 0x00000DE7, 0x00000DE6, 0x00000DE5, 0x00000DE3,
	0x00000DE2, 0x00000DE1, 0x00000DE0, 0x00000DDE, 0x00000DDD, 0x00000DDC, 0x00000DDA, 0x00000DD9,
	0x00000DD8, 0x00000DD6, 0x00000DD5, 0x00000DD4, 0x00000DD2, 0x00000DD1, 0x00000DD0, 0x00000DCE,
	0x00000DCD, 0x00000DCC, 0x00000DCA, 0x00000DC9, 0x00000DC8, 0x00000DC7, 0x00000DC5, 0x00000DC4,
	0x00000DC3, 0x00000DC1, 0x00000DC0, 0x00000DBF, 0x00000DBD, 0x00000DBC, 0x00000DBB, 0x00000DB9,
	0x00000DB8, 0x00000DB7, 0x00000DB5, 0x00000DB4, 0x00000DB3, 0x00000DB1, 0x00000DB0, 0x00000DAF,
	0x00000DAD, 0x00000DAC, 0x00000DAB, 0x00000DA9, 0x00000DA8, 0x00000DA7, 0x00000DA5, 0x00000DA4,
	0x00000DA3, 0x00000DA1, 0x00000DA0, 0x00000D9F, 0x00000D9D, 0x00000D9C, 0x00000D9B, 0x00000D99,
	0x00000D98, 0x00000D97, 0x00000D96, 0x00000D94, 0x00000D93, 0x00000D92, 0x00000D90, 0x00000D8F,
	0x00000D8E, 0x00000D8C, 0x00000D8B, 0x00000D89, 0x00000D88, 0x00000D87, 0x00000D85, 0x00000D84,
	0x00000D83, 0x00000D81, 0x00000D80, 0x00000D7F, 0x00000D7D, 0x00000D7C, 0x00000D7B, 0x00000D79,
	0x00000D78, 0x00000D77, 0x00000D75, 0x00000D74, 0x00000D73, 0x00000D71, 0x00000D70, 0x00000D6F,
	0x00000D6D, 0x00000D6C, 0x00000D6B, 0x00000D69, 0x00000D68, 0x00000D67, 0x00000D65, 0x00000D64,
	0x00000D63, 0x00000D61, 0x00000D60, 0x00000D5F, 0x00000D5D, 0x00000D5C, 0x00000D5B, 0x00000D59,
	0x00000D58, 0x00000D56, 0x00000D55, 0x00000D54, 0x00000D52, 0x00000D51, 0x00000D50, 0x00000D4E,
	0x00000D4D, 0x00000D4C, 0x00000D4A, 0x00000D49, 0x00000D48, 0x00000D46, 0x00000D45, 0x00000D44,
	0x00000D42, 0x00000D41, 0x00000D3F, 0x00000D3E, 0x00000D3D, 0x00000D3B, 0x00000D3A, 0x00000D39,
	0x00000D37, 0x00000D36, 0x00000D35, 0x00000D33, 0x00000D32, 0x00000D30, 0x00000D2F, 0x00000D2E,
	0x00000D2C, 0x00000D2B, 0x00000D2A, 0x00000D28, 0x00000D27, 0x00000D26, 0x00000D24, 0x00000D23,
	0x00000D21, 0x00000D20, 0x00000D1F, 0x00000D1D, 0x00000D1C, 0x00000D1B, 0x00000D19, 0x00000D18,
	0x00000D17, 0x00000D15, 0x00000D14, 0x00000D12, 0x00000D11, 0x00000D10, 0x00000D0E, 0x00000D0D,
	0x00000D0C, 0x00000D0A, 0x00000D09, 0x00000D07, 0x00000D06, 0x00000D05, 0x00000D03, 0x00000D02,
	0x00000D01, 0x00000CFF, 0x00000CFE, 0x00000CFC, 0x00000CFB, 0x00000CFA, 0x00000CF8, 0x00000CF7,
	0x00000CF6, 0x00000CF4, 0x00000CF3, 0x00000CF1, 0x00000CF0, 0x00000CEF, 0x00000CED, 0x00000CEC,
	0x00000CEA, 0x00000CE9, 0x00000CE8, 0x00000CE6, 0x00000CE5, 0x00000CE4, 0x00000CE2, 0x00000CE1,
	0x00000CDF, 0x00000CDE, 0x00000CDD, 0x00000CDB, 0x00000CDA, 0x00000CD8, 0x00000CD7, 0x00000CD6,
	0x00000CD4, 0x00000CD3, 0x00000CD1, 0x00000CD0, 0x00000CCF, 0x00000CCD, 0x00000CCC, 0x00000CCB,
	0x00000CC9, 0x00000CC8, 0x00000CC6, 0x00000CC5, 0x00000CC4, 0x00000CC2, 0x00000CC1, 0x00000CBF,
	0x00000CBE, 0x00000CBD, 0x00000CBB, 0x00000CBA, 0x00000CB8, 0x00000CB7, 0x00000CB6, 0x00000CB4,
	0x00000CB3, 0x00000CB1, 0x00000CB0, 0x00000CAF, 0x00000CAD, 0x00000CAC, 0x00000CAA, 0x00000CA9,
	0x00000CA8, 0x00000CA6, 0x00000CA5, 0x00000CA3, 0x00000CA2, 0x00000CA0, 0x00000C9F, 0x00000C9E,
	0x00000C9C, 0x00000C9B, 0x00000C99, 0x00000C98, 0x00000C97, 0x00000C95, 0x00000C94, 0x00000C92,
	0x00000C91, 0x00000C90, 0x00000C8E, 0x00000C8D, 0x00000C8B, 0x00000C8A, 0x00000C88, 0x00000C87,
	0x00000C86, 0x00000C84, 0x00000C83, 0x00000C81, 0x00000C80, 0x00000C7F, 0x00000C7D, 0x00000C7C,
	0x00000C7A, 0x00000C79, 0x00000C77, 0x00000C76, 0x00000C75, 0x00000C73, 0x00000C72, 0x00000C70,
	0x00000C6F, 0x00000C6D, 0x00000C6C, 0x00000C6B, 0x00000C69, 0x00000C68, 0x00000C66, 0x00000C65,
	0x00000C63, 0x00000C62, 0x00000C61, 0x00000C5F, 0x00000C5E, 0x00000C5C, 0x00000C5B, 0x00000C59,
	0x00000C58, 0x00000C57, 0x00000C55, 0x00000C54, 0x00000C52, 0x00000C51, 0x00000C4F, 0x00000C4E,
	0x00000C4C, 0x00000C4B, 0x00000C4A, 0x00000C48, 0x00000C47, 0x00000C45, 0x00000C44, 0x00000C42,
	0x00000C41, 0x00000C40, 0x00000C3E, 0x00000C3D, 0x00000C3B, 0x00000C3A, 0x00000C38, 0x00000C37,
	0x00000C35, 0x00000C34, 0x00000C33, 0x00000C31, 0x00000C30, 0x00000C2E, 0x00000C2D, 0x00000C2B,
	0x00000C2A, 0x00000C28, 0x00000C27, 0x00000C25, 0x00000C24, 0x00000C23, 0x00000C21, 0x00000C20,
	0x00000C1E, 0x00000C1D, 0x00000C1B, 0x00000C1A, 0x00000C18, 0x00000C17, 0x00000C15, 0x00000C14,
	0x00000C13, 0x00000C11, 0x00000C10, 0x00000C0E, 0x00000C0D, 0x00000C0B, 0x00000C0A, 0x00000C08,
	0x00000C07, 0x00000C05, 0x00000C04, 0x00000C02, 0x00000C01, 0x00000C00, 0x00000BFE, 0x00000BFD,
	0x00000BFB, 0x00000BFA, 0x00000BF8, 0x00000BF7, 0x00000BF5, 0x00000BF4, 0x00000BF2, 0x00000BF1,
	0x00000BEF, 0x00000BEE, 0x00000BEC, 0x00000BEB, 0x00000BE9, 0x00000BE8, 0x00000BE6, 0x00000BE5,
	0x00000BE4, 0x00000BE2, 0x00000BE1, 0x00000BDF, 0x00000BDE, 0x00000BDC, 0x00000BDB, 0x00000BD9,
	0x00000BD8, 0x00000BD6, 0x00000BD5, 0x00000BD3, 0x00000BD2, 0x00000BD0, 0x00000BCF, 0x00000BCD,
	0x00000BCC, 0x00000BCA, 0x00000BC9, 0x00000BC7, 0x00000BC6, 0x00000BC4, 0x00000BC3, 0x00000BC1,
	0x00000BC0, 0x00000BBE, 0x00000BBD, 0x00000BBB, 0x00000BBA, 0x00000BB8, 0x00000BB7, 0x00000BB5,
	0x00000BB4, 0x00000BB2, 0x00000BB1, 0x00000BAF, 0x00000BAE, 0x00000BAC, 0x00000BAB, 0x00000BA9,
	0x00000BA8, 0x00000BA6, 0x00000BA5, 0x00000BA3, 0x00000BA2, 0x00000BA0, 0x00000B9F, 0x00000B9D,
	0x00000B9C, 0x00000B9A, 0x00000B99, 0x00000B97, 0x00000B96, 0x00000B94, 0x00000B93, 0x00000B91,
	0x00000B90, 0x00000B8E, 0x00000B8D, 0x00000B8B, 0x00000B8A, 0x00000B88, 0x00000B87, 0x00000B85,
	0x00000B84, 0x00000B82, 0x00000B81, 0x00000B7F, 0x00000B7E, 0x00000B7C, 0x00000B7B, 0x00000B79,
	0x00000B78, 0x00000B76, 0x00000B75, 0x00000B73, 0x00000B71, 0x00000B70, 0x00000B6E, 0x00000B6D,
	0x00000B6B, 0x00000B6A, 0x00000B68, 0x00000B67, 0x00000B65, 0x00000B64, 0x00000B62, 0x00000B61,
	0x00000B5F, 0x00000B5E, 0x00000B5C, 0x00000B5B, 0x00000B59, 0x00000B57, 0x00000B56, 0x00000B54,
	0x00000B53, 0x00000B51, 0x00000B50, 0x00000B4E, 0x00000B4D, 0x00000B4B, 0x00000B4A, 0x00000B48,
	0x00000B47, 0x00000B45, 0x00000B43, 0x00000B42, 0x00000B40, 0x00000B3F, 0x00000B3D, 0x00000B3C,
	0x00000B3A, 0x00000B39, 0x00000B37, 0x00000B35, 0x00000B34, 0x00000B32, 0x00000B31, 0x00000B2F,
	0x00000B2E, 0x00000B2C, 0x00000B2B, 0x00000B29, 0x00000B28, 0x00000B26, 0x00000B24, 0x00000B23,
	0x00000B21, 0x00000B20, 0x00000B1E, 0x00000B1D, 0x00000B1B, 0x00000B19, 0x00000B18, 0x00000B16,
	0x00000B15, 0x00000B13, 0x00000B12, 0x00000B10, 0x00000B0E, 0x00000B0D, 0x00000B0B, 0x00000B0A,
	0x00000B08, 0x00000B07, 0x00000B05, 0x00000B03, 0x00000B02, 0x00000B00, 0x00000AFF, 0x00000AFD,
	0x00000AFC, 0x00000AFA, 0x00000AF8, 0x00000AF7, 0x00000AF5, 0x00000AF4, 0x00000AF2, 0x00000AF1,
	0x00000AEF, 0x00000AED, 0x00000AEC, 0x00000AEA, 0x00000AE9, 0x00000AE7, 0x00000AE5, 0x00000AE4,
	0x00000AE2, 0x00000AE1, 0x00000ADF, 0x00000ADE, 0x00000ADC, 0x00000ADA, 0x00000AD9, 0x00000AD7,
	0x00000AD6, 0x00000AD4, 0x00000AD2, 0x00000AD1, 0x00000ACF, 0x00000ACE, 0x00000ACC, 0x00000ACA,
	0x00000AC9, 0x00000AC7, 0x00000AC6, 0x00000AC4, 0x00000AC2, 0x00000AC1, 0x00000ABF, 0x00000ABE,
	0x00000ABC, 0x00000ABA, 0x00000AB9, 0x00000AB7, 0x00000AB5, 0x00000AB4, 0x00000AB2, 0x00000AB1,
	0x00000AAF, 0x00000AAD, 0x00000AAC, 0x00000AAA, 0x00000AA9, 0x00000AA7, 0x00000AA5, 0x00000AA4,
	0x00000AA2, 0x00000AA0, 0x00000A9F, 0x00000A9D, 0x00000A9C, 0x00000A9A, 0x00000A98, 0x00000A97,
	0x00000A95, 0x00000A93, 0x00000A92, 0x00000A90, 0x00000A8F, 0x00000A8D, 0x00000A8B, 0x00000A8A,
	0x00000A88, 0x00000A86, 0x00000A85, 0x00000A83, 0x00000A81, 0x00000A80, 0x00000A7E, 0x00000A7D,
	0x00000A7B, 0x00000A79, 0x00000A78, 0x00000A76, 0x00000A74, 0x00000A73, 0x00000A71, 0x00000A6F,
	0x00000A6E, 0x00000A6C, 0x00000A6A, 0x00000A69, 0x00000A67, 0x00000A66, 0x00000A64, 0x00000A62,
	0x00000A61, 0x00000A5F, 0x00000A5D, 0x00000A5C, 0x00000A5A, 0x00000A58, 0x00000A57, 0x00000A55,
	0x00000A53, 0x00000A52, 0x00000A50, 0x00000A4E, 0x00000A4D, 0x00000A4B, 0x00000A49, 0x00000A48,
	0x00000A46, 0x00000A44, 0x00000A43, 0x00000A41, 0x00000A3F, 0x00000A3E, 0x00000A3C, 0x00000A3A,
	0x00000A39, 0x00000A37, 0x00000A35, 0x00000A34, 0x00000A32, 0x00000A30, 0x00000A2E, 0x00000A2D,
	0x00000A2B, 0x00000A29, 0x00000A28, 0x00000A26, 0x00000A24, 0x00000A23, 0x00000A21, 0x00000A1F,
	0x00000A1E, 0x00000A1C, 0x00000A1A, 0x00000A19, 0x00000A17, 0x00000A15, 0x00000A13, 0x00000A12,
	0x00000A10, 0x00000A0E, 0x00000A0D, 0x00000A0B, 0x00000A09, 0x00000A08, 0x00000A06, 0x00000A04,
	0x00000A02, 0x00000A01, 0x000009FF, 0x000009FD, 0x000009FC, 0x000009FA, 0x000009F8, 0x000009F6,
	0x000009F5, 0x000009F3, 0x000009F1, 0x000009F0, 0x000009EE, 0x000009EC, 0x000009EA, 0x000009E9,
	0x000009E7, 0x000009E5, 0x000009E4, 0x000009E2, 0x000009E0, 0x000009DE, 0x000009DD, 0x000009DB,
	0x000009D9, 0x000009D7, 0x000009D6, 0x000009D4, 0x000009D2, 0x000009D0, 0x000009CF, 0x000009CD,
	0x000009CB, 0x000009CA, 0x000009C8, 0x000009C6, 0x000009C4, 0x000009C3, 0x000009C1, 0x000009BF,
	0x000009BD, 0x000009BC, 0x000009BA, 0x000009B8, 0x000009B6, 0x000009B5, 0x000009B3, 0x000009B1,
	0x000009AF, 0x000009AE, 0x000009AC, 0x000009AA, 0x000009A8, 0x000009A6, 0x000009A5, 0x000009A3,
	0x000009A1, 0x0000099F, 0x0000099E, 0x0000099C, 0x0000099A, 0x00000998, 0x00000997, 0x00000995,
	0x00000993, 0x00000991, 0x0000098F, 0x0000098E, 0x0000098C, 0x0000098A, 0x00000988, 0x00000987,
	0x00000985, 0x00000983, 0x00000981, 0x0000097F, 0x0000097E, 0x0000097C, 0x0000097A, 0x00000978,
	0x00000976, 0x00000975, 0x00000973, 0x00000971, 0x0000096F, 0x0000096E, 0x0000096C, 0x0000096A,
	0x00000968, 0x00000966, 0x00000964, 0x00000963, 0x00000961, 0x0000095F, 0x0000095D, 0x0000095B,
	0x0000095A, 0x00000958, 0x00000956, 0x00000954, 0x00000952, 0x00000951, 0x0000094F, 0x0000094D,
	0x0000094B, 0x00000949, 0x00000947, 0x00000946, 0x00000944, 0x00000942, 0x00000940, 0x0000093E,
	0x0000093C, 0x0000093B, 0x00000939, 0x00000937, 0x00000935, 0x00000933, 0x00000931, 0x00000930,
	0x0000092E, 0x0000092C, 0x0000092A, 0x00000928, 0x00000926, 0x00000925, 0x00000923, 0x00000921,
	0x0000091F, 0x0000091D, 0x0000091B, 0x00000919, 0x00000918, 0x00000916, 0x00000914, 0x00000912,
	0x00000910, 0x0000090E, 0x0000090C, 0x0000090B, 0x00000909, 0x00000907, 0x00000905, 0x00000903,
	0x00000901, 0x000008FF, 0x000008FD, 0x000008FC, 0x000008FA, 0x000008F8, 0x000008F6, 0x000008F4,
	0x000008F2, 0x000008F0, 0x000008EE, 0x000008ED, 0x000008EB, 0x000008E9, 0x000008E7, 0x000008E5,
	0x000008E3, 0x000008E1, 0x000008DF, 0x000008DD, 0x000008DB, 0x000008DA, 0x000008D8, 0x000008D6,
	0x000008D4, 0x000008D2, 0x000008D0, 0x000008CE, 0x000008CC, 0x000008CA, 0x000008C8, 0x000008C6,
	0x000008C5, 0x000008C3, 0x000008C1, 0x000008BF, 0x000008BD, 0x000008BB, 0x000008B9, 0x000008B7,
	0x000008B5, 0x000008B3, 0x000008B1, 0x000008AF, 0x000008AD, 0x000008AB, 0x000008AA, 0x000008A8,
	0x000008A6, 0x000008A4, 0x000008A2, 0x000008A0, 0x0000089E, 0x0000089C, 0x0000089A, 0x00000898,
	0x00000896, 0x00000894, 0x00000892, 0x00000890, 0x0000088E, 0x0000088C, 0x0000088A, 0x00000888,
	0x00000886, 0x00000884, 0x00000882, 0x00000880, 0x0000087E, 0x0000087C, 0x0000087B, 0x00000879,
	0x00000877, 0x00000875, 0x00000873, 0x00000871, 0x0000086F, 0x0000086D, 0x0000086B, 0x00000869,
	0x00000867, 0x00000865, 0x00000863, 0x00000861, 0x0000085F, 0x0000085D, 0x0000085B, 0x00000859,
	0x00000857, 0x00000855, 0x00000853, 0x00000851, 0x0000084F, 0x0000084D, 0x0000084B, 0x00000849,
	0x00000847, 0x00000844, 0x00000842, 0x00000840, 0x0000083E, 0x0000083C, 0x0000083A, 0x00000838,
	0x00000836, 0x00000834, 0x00000832, 0x00000830, 0x0000082E, 0x0000082C, 0x0000082A, 0x00000828,
	0x00000826, 0x00000824, 0x00000822, 0x00000820, 0x0000081E, 0x0000081C, 0x0000081A, 0x00000818,
	0x00000815, 0x00000813, 0x00000811, 0x0000080F, 0x0000080D, 0x0000080B, 0x00000809, 0x00000807,
	0x00000805, 0x00000803, 0x00000801, 0x000007FF, 0x000007FD, 0x000007FA, 0x000007F8, 0x000007F6,
	0x000007F4, 0x000007F2, 0x000007F0, 0x000007EE, 0x000007EC, 0x000007EA, 0x000007E8, 0x000007E5,
	0x000007E3, 0x000007E1, 0x000007DF, 0x000007DD, 0x000007DB, 0x000007D9, 0x000007D7, 0x000007D4,
	0x000007D2, 0x000007D0, 0x000007CE, 0x000007CC, 0x000007CA, 0x000007C8, 0x000007C6, 0x000007C3,
	0x000007C1, 0x000007BF, 0x000007BD, 0x000007BB, 0x000007B9, 0x000007B7, 0x000007B4, 0x000007B2,
	0x000007B0, 0x000007AE, 0x000007AC, 0x000007AA, 0x000007A7, 0x000007A5, 0x000007A3, 0x000007A1,
	0x0000079F, 0x0000079C, 0x0000079A, 0x00000798, 0x00000796, 0x00000794, 0x00000792, 0x0000078F,
	0x0000078D, 0x0000078B, 0x00000789, 0x00000787, 0x00000784, 0x00000782, 0x00000780, 0x0000077E,
	0x0000077B, 0x00000779, 0x00000777, 0x00000775, 0x00000773, 0x00000770, 0x0000076E, 0x0000076C,
	0x0000076A, 0x00000767, 0x00000765, 0x00000763, 0x00000761, 0x0000075E, 0x0000075C, 0x0000075A,
	0x00000758, 0x00000755, 0x00000753, 0x00000751, 0x0000074F, 0x0000074C, 0x0000074A, 0x00000748,
	0x00000746, 0x00000743, 0x00000741, 0x0000073F, 0x0000073C, 0x0000073A, 0x00000738, 0x00000736,
	0x00000733, 0x00000731, 0x0000072F, 0x0000072C, 0x0000072A, 0x00000728, 0x00000725, 0x00000723,
	0x00000721, 0x0000071E, 0x0000071C, 0x0000071A, 0x00000717, 0x00000715, 0x00000713, 0x00000710,
	0x0000070E, 0x0000070C, 0x00000709, 0x00000707, 0x00000705, 0x00000702, 0x00000700, 0x000006FE,
	0x000006FB, 0x000006F9, 0x000006F7, 0x000006F4, 0x000006F2, 0x000006EF, 0x000006ED, 0x000006EB,
	0x000006E8, 0x000006E6, 0x000006E3, 0x000006E1, 0x000006DF, 0x000006DC, 0x000006DA, 0x000006D7,
	0x000006D5, 0x000006D3, 0x000006D0, 0x000006CE, 0x000006CB, 0x000006C9, 0x000006C7, 0x000006C4,
	0x000006C2, 0x000006BF, 0x000006BD, 0x000006BA, 0x000006B8, 0x000006B5, 0x000006B3, 0x000006B0,
	0x000006AE, 0x000006AC, 0x000006A9, 0x000006A7, 0x000006A4, 0x000006A2, 0x0000069F, 0x0000069D,
	0x0000069A, 0x00000698, 0x00000695, 0x00000693, 0x00000690, 0x0000068E, 0x0000068B, 0x00000689,
	0x00000686, 0x00000684, 0x00000681, 0x0000067F, 0x0000067C, 0x00000679, 0x00000677, 0x00000674,
	0x00000672, 0x0000066F, 0x0000066D, 0x0000066A, 0x00000668, 0x00000665, 0x00000662, 0x00000660,
	0x0000065D, 0x0000065B, 0x00000658, 0x00000656, 0x00000653, 0x00000650, 0x0000064E, 0x0000064B,
	0x00000649, 0x00000646, 0x00000643, 0x00000641, 0x0000063E, 0x0000063B, 0x00000639, 0x00000636,
	0x00000634, 0x00000631, 0x0000062E, 0x0000062C, 0x00000629, 0x00000626, 0x00000624, 0x00000621,
	0x0000061E, 0x0000061C, 0x00000619, 0x00000616, 0x00000613, 0x00000611, 0x0000060E, 0x0000060B,
	0x00000609, 0x00000606, 0x00000603, 0x00000600, 0x000005FE, 0x000005FB, 0x000005F8, 0x000005F6,
	0x000005F3, 0x000005F0, 0x000005ED, 0x000005EB, 0x000005E8, 0x000005E5, 0x000005E2, 0x000005DF,
	0x000005DD, 0x000005DA, 0x000005D7, 0x000005D4, 0x000005D1, 0x000005CF, 0x000005CC, 0x000005C9,
	0x000005C6, 0x000005C3, 0x000005C0, 0x000005BE, 0x000005BB, 0x000005B8, 0x000005B5, 0x000005B2,
	0x000005AF, 0x000005AC, 0x000005AA, 0x000005A7, 0x000005A4, 0x000005A1, 0x0000059E, 0x0000059B,
	0x00000598, 0x00000595, 0x00000592, 0x0000058F, 0x0000058C, 0x00000589, 0x00000587, 0x00000584,
	0x00000581, 0x0000057E, 0x0000057B, 0x00000578, 0x00000575, 0x00000572, 0x0000056F, 0x0000056C,
	0x00000569, 0x00000566, 0x00000563, 0x00000560, 0x0000055D, 0x0000055A, 0x00000556, 0x00000553,
	0x00000550, 0x0000054D, 0x0000054A, 0x00000547, 0x00000544, 0x00000541, 0x0000053E, 0x0000053B,
	0x00000538, 0x00000535, 0x00000531, 0x0000052E, 0x0000052B, 0x00000528, 0x00000525, 0x00000522,
	0x0000051E, 0x0000051B, 0x00000518, 0x00000515, 0x00000512, 0x0000050E, 0x0000050B, 0x00000508,
	0x00000505, 0x00000502, 0x000004FE, 0x000004FB, 0x000004F8, 0x000004F4, 0x000004F1, 0x000004EE,
	0x000004EB, 0x000004E7, 0x000004E4, 0x000004E1, 0x000004DD, 0x000004DA, 0x000004D7, 0x000004D3,
	0x000004D0, 0x000004CD, 0x000004C9, 0x000004C6, 0x000004C2, 0x000004BF, 0x000004BB, 0x000004B8,
	0x000004B5, 0x000004B1, 0x000004AE, 0x000004AA, 0x000004A7, 0x000004A3, 0x000004A0, 0x0000049C,
	0x00000499, 0x00000495, 0x00000492, 0x0000048E, 0x0000048A, 0x00000487, 0x00000483, 0x00000480,
	0x0000047C, 0x00000479, 0x00000475, 0x00000471, 0x0000046E, 0x0000046A, 0x00000466, 0x00000463,
	0x0000045F, 0x0000045B, 0x00000457, 0x00000454, 0x00000450, 0x0000044C, 0x00000448, 0x00000445,
	0x00000441, 0x0000043D, 0x00000439, 0x00000435, 0x00000432, 0x0000042E, 0x0000042A, 0x00000426,
	0x00000422, 0x0000041E, 0x0000041A, 0x00000416, 0x00000412, 0x0000040E, 0x0000040A, 0x00000406,
	0x00000402, 0x000003FE, 0x000003FA, 0x000003F6, 0x000003F2, 0x000003EE, 0x000003EA, 0x000003E6,
	0x000003E1, 0x000003DD, 0x000003D9, 0x000003D5, 0x000003D1, 0x000003CC, 0x000003C8, 0x000003C4,
	0x000003C0, 0x000003BB, 0x000003B7, 0x000003B3, 0x000003AE, 0x000003AA, 0x000003A5, 0x000003A1,
	0x0000039C, 0x00000398, 0x00000394, 0x0000038F, 0x0000038A, 0x00000386, 0x00000381, 0x0000037D,
	0x00000378, 0x00000373, 0x0000036F, 0x0000036A, 0x00000365, 0x00000361, 0x0000035C, 0x00000357,
	0x00000352, 0x0000034D, 0x00000348, 0x00000343, 0x0000033E, 0x00000339, 0x00000334, 0x0000032F,
	0x0000032A, 0x00000325, 0x00000320, 0x0000031B, 0x00000316, 0x00000311, 0x0000030B, 0x00000306,
	0x00000301, 0x000002FB, 0x000002F6, 0x000002F0, 0x000002EB, 0x000002E5, 0x000002E0, 0x000002DA,
	0x000002D5, 0x000002CF, 0x000002C9, 0x000002C3, 0x000002BD, 0x000002B8, 0x000002B2, 0x000002AC,
	0x000002A6, 0x0000029F, 0x00000299, 0x00000293, 0x0000028D, 0x00000287, 0x00000280, 0x0000027A,
	0x00000273, 0x0000026D, 0x00000266, 0x0000025F, 0x00000258, 0x00000252, 0x0000024B, 0x00000244,
	0x0000023C, 0x00000235, 0x0000022E, 0x00000226, 0x0000021F, 0x00000217, 0x00000210, 0x00000208,
	0x00000200, 0x000001F8, 0x000001F0, 0x000001E7, 0x000001DF, 0x000001D6, 0x000001CD, 0x000001C4,
	0x000001BB, 0x000001B2, 0x000001A8, 0x0000019E, 0x00000194, 0x0000018A, 0x00000180, 0x00000175,
	0x0000016A, 0x0000015E, 0x00000152, 0x00000146, 0x00000139, 0x0000012C, 0x0000011E, 0x0000010F,
	0x00000100, 0x000000EF, 0x000000DD, 0x000000CA, 0x000000B5, 0x0000009C, 0x00000080, 0x0000005A
};


static Int intArcCos( Int c )
{
	c += INT_ONE;
	if (c < 0)
		c = 0;

	if (c >= 2 * INT_ONE)
		c = (2 * INT_ONE)-1;

	return arcCosLookup[c];
}

static Int intSin( Int angle )
{
	while (angle < 0)
		angle += INT_TWOPI;

	while (angle >= INT_TWOPI)
		angle -= INT_TWOPI;

	return sinLookup[(angle * TRIG_RES)/INT_TWOPI];
}

static Int intTan( Int angle )
{
	Int s, c;

	s = intSin(angle);
	c = intCos(angle);

	if (c == 0) {
		if (s > 0)
			return INT_MAX;
		else
			return INT_MIN;
	}

	return INT_ONE * s / c;
}

// These calls are somewhat less accurate than the standard library calls, but use a
// precalculated lookup table for speed and repeatability across different machines
#define DEFAULT_TRIG
Real Sin(Real x)
{
#ifdef DEFAULT_TRIG
	return sinf(x);
#else
	return (Real)intSin((Int)((x) * INT_ONE)) / (Real)INT_ONE;
#endif
}

Real Cos(Real x)
{
#ifdef DEFAULT_TRIG
	return cosf(x);
#else
	return (Real)intCos((Int)((x) * INT_ONE)) / (Real)INT_ONE;
#endif
}

Real Tan(Real x)
{
#ifdef DEFAULT_TRIG
	return tanf(x);
#else
	return (Real)intTan((Int)((x) * INT_ONE)) / (Real)INT_ONE;
#endif
}

Real ACos(Real x)
{
#ifdef DEFAULT_TRIG
	return acosf(x);
#else
	return (Real)(intArcCos((Int)((x) * INT_ONE))) * TWOPI / (Real)INT_TWOPI;
#endif
}

Real ASin(Real x)
{
	return asinf(x);
}

#ifdef REGENERATE_TRIG_TABLES
#include <stdio.h>
void initTrig( void )
{
	static Byte inited = FALSE;
	Real angle, r;
	int i;

	if (inited)
		return;

	inited = TRUE;

	static int columns = 8;
	int column = 0;
	FILE *fp = fopen("trig.txt", "w");
	fprintf(fp, "static Int sinLookup[TRIG_RES] = {\n");
	for( i=0; i<TRIG_RES; i++ ) {
		angle = TWOPI * i / (Real)TRIG_RES;
		sinLookup[i] = (Int)(sin(angle) * INT_ONE);

		if (i == 0)
		{
			fprintf(fp, "\t0x%8.8X", sinLookup[i]);
		}
		else if (column == 0)
		{
		fprintf(fp, ",\n\t0x%8.8X", sinLookup[i]);
		}
		else
		{
		fprintf(fp, ", 0x%8.8X", sinLookup[i]);
		}
		column = (column + 1) % columns;
	}
	fprintf(fp, "\n};\n\n");

	column = 0;
	fprintf(fp, "static Int arcCosLookup[2 * INT_ONE] = {\n");
	for( i=0; i<2*INT_ONE; i++ ) {
		r = (Real)i / (Real)INT_ONE - 1.0f;

		arcCosLookup[i] = (Int)(acos( (double)r ) * INT_TWOPI / TWOPI );

		if (i == 0)
		{
			fprintf(fp, "\t0x%8.8X", arcCosLookup[i]);
		}
		else if (column == 0)
		{
		fprintf(fp, ",\n\t0x%8.8X", arcCosLookup[i]);
		}
		else
		{
		fprintf(fp, ", 0x%8.8X", arcCosLookup[i]);
		}
		column = (column + 1) % columns;
	}
	fprintf(fp, "\n};\n\n");

	fclose(fp);
}

class TrigInit
{
public:
	TrigInit() { initTrig(); }
};
TrigInit trigInitializer;

#endif // REGENERATE_TRIG_TABLES


