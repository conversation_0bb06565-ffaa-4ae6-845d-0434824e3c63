/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// FILE: GameWindowID.h ///////////////////////////////////////////////////////
//-----------------------------------------------------------------------------
//                                                                          
//                       Westwood Studios Pacific.                          
//                                                                          
//                       Confidential Information					         
//                Copyright (C) 2001 - All Rights Reserved                  
//                                                                          
//-----------------------------------------------------------------------------
//
// Project:    RTS3
//
// File name:  GameWindowID.h
//
// Created:    Colin Day, July 2001
//
// Desc:			 Game window ID definitions, this is so we can uniquely
//						 identify any window in the game and pass messages from
//						 anywhere to anywhere else instantly
//
//-----------------------------------------------------------------------------
///////////////////////////////////////////////////////////////////////////////

#pragma once

#ifndef __GAMEWINDOWID_H_
#define __GAMEWINDOWID_H_

// SYSTEM INCLUDES ////////////////////////////////////////////////////////////

// USER INCLUDES //////////////////////////////////////////////////////////////

// FORWARD REFERENCES /////////////////////////////////////////////////////////

// TYPE DEFINES ///////////////////////////////////////////////////////////////
#define GWID_INVALID 0

#define GWID_SIDEBAR							100

#define GWID_SIDEBAR_1						101
#define GWID_SIDEBAR_2						102
#define GWID_SIDEBAR_3						103
#define GWID_SIDEBAR_4						104
#define GWID_SIDEBAR_5						105
#define GWID_SIDEBAR_6						106
#define GWID_SIDEBAR_7						107
#define GWID_SIDEBAR_8						108
#define GWID_SIDEBAR_9						109
#define GWID_SIDEBAR_10						110
#define GWID_SIDEBAR_11						111
#define GWID_SIDEBAR_12						112

#define GWID_SIDEBAR_TAB_1				151
#define GWID_SIDEBAR_TAB_2				152
#define GWID_SIDEBAR_TAB_3				153
#define GWID_SIDEBAR_TAB_4				154

#define GWID_SIDEBAR_RADAR				175

// INLINING ///////////////////////////////////////////////////////////////////

// EXTERNALS //////////////////////////////////////////////////////////////////

#endif // __GAMEWINDOWID_H_

