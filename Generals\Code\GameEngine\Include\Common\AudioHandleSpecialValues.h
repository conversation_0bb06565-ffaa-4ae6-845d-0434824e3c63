/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// EA Pacific
// John McDonald, Jr
// Do not distribute

#pragma once

#ifndef _AUDIOHANDLESPECIALVALUES_H_
#define _AUDIOHANDLESPECIALVALUES_H_

enum AudioHandleSpecialValues
{
	AHSV_Error					= 0x00,
	AHSV_NoSound,
	AHSV_Muted,
	AHSV_NotForLocal,
	AHSV_StopTheMusic,
	AHSV_StopTheMusicFade,
	AHSV_FirstHandle
};

#endif // _AUDIOHANDLESPECIALVALUES_H_
