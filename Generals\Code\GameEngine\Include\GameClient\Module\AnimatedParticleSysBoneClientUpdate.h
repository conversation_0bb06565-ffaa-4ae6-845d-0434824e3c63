/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// FILE: AnimatedParticleSysBoneClientUpdate.h //////////////////////////////////////////////////////////////////
// Author: Mark Lorenzen, October 2002
// Desc:   client update module to translate particle systems with animation
///////////////////////////////////////////////////////////////////////////////////////////////////

#pragma once

#ifndef __ANIMPARTICLESYSBONEUPDATE_H_
#define __ANIMPARTICLESYSBONEUPDATE_H_

// INCLUDES ///////////////////////////////////////////////////////////////////////////////////////
#include "Common/ClientUpdateModule.h"

// FORWARD REFERENCES /////////////////////////////////////////////////////////////////////////////
class Thing;

class AnimatedParticleSysBoneClientUpdate : public ClientUpdateModule
{

	MEMORY_POOL_GLUE_WITH_USERLOOKUP_CREATE( AnimatedParticleSysBoneClientUpdate, "AnimatedParticleSysBoneClientUpdate" )
	MAKE_STANDARD_MODULE_MACRO( AnimatedParticleSysBoneClientUpdate );

public:

	AnimatedParticleSysBoneClientUpdate( Thing *thing, const ModuleData* moduleData );
	// virtual destructor prototype provided by memory pool declaration

	/// the client update callback
	virtual void clientUpdate( void );


protected:


	UnsignedInt m_life;

};

#endif // __ANIMPARTICLESYSBONEUPDATE_H_

