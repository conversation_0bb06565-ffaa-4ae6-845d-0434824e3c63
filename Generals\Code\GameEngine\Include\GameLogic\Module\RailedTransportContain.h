/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// FILE: RailedTransportContain.h /////////////////////////////////////////////////////////////////
// Author: Colin Day, August 2002
// Desc: Railed transport contain module
///////////////////////////////////////////////////////////////////////////////////////////////////

#pragma once

#ifndef __RAILED_TRANSPORT_CONTAIN_H_
#define __RAILED_TRANSPORT_CONTAIN_H_

// INCLUDES ///////////////////////////////////////////////////////////////////////////////////////
#include "GameLogic/Module/TransportContain.h"

// ------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------
class RailedTransportContain : public TransportContain
{

	MEMORY_POOL_GLUE_WITH_USERLOOKUP_CREATE( RailedTransportContain, "RailedTransportContain" )
	MAKE_STANDARD_MODULE_MACRO( RailedTransportContain )

public:

	RailedTransportContain( Thing *thing, const ModuleData* moduleData );
	// virtual destructor prototype provided by memory pool declaration

	virtual void onRemoving( Object *obj );			///< object no longer contains 'obj'
	virtual void exitObjectViaDoor( Object *newObj, ExitDoorType exitDoor );
	virtual void exitObjectByBudding( Object *newObj, Object *budHost ) { return; };

protected:

	virtual Bool isSpecificRiderFreeToExit( Object *obj );

};

#endif  // end __RAILED_TRANSPORT_CONTAIN_H_
