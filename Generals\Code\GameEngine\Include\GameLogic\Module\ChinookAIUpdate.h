/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

// ChinookAIUpdate.h //////////
// Author: Steven Johnson, June 2002
 
#pragma once

#ifndef _ChinookAIUpdate_H_
#define _ChinookAIUpdate_H_

#include "GameLogic/AIStateMachine.h"
#include "GameLogic/Module/SupplyTruckAIUpdate.h"

//-------------------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------------------

//-------------------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------------------
//-------------------------------------------------------------------------------------------------

//-------------------------------------------------------------------------------------------------
class ChinookAIUpdateModuleData : public SupplyTruckAIUpdateModuleData
{
public:
	AsciiString		m_ropeName;
	Real					m_rappelSpeed;
	Real					m_ropeDropSpeed;
	Real					m_ropeWidth;
	Real					m_ropeFinalHeight;
	Real					m_ropeWobbleLen;
	Real					m_ropeWobbleAmp;
	Real					m_ropeWobbleRate;
	RGBColor			m_ropeColor;
	UnsignedInt		m_numRopes;
	UnsignedInt		m_perRopeDelayMin;
	UnsignedInt		m_perRopeDelayMax;
	Real					m_minDropHeight;
	Bool					m_waitForRopesToDrop;

	ChinookAIUpdateModuleData();
	static void buildFieldParse(MultiIniFieldParse& p);
};

//-------------------------------------------------------------------------------------------------
enum ChinookFlightStatus // Stored in save file, don't renumber.  jba. 
{
	CHINOOK_TAKING_OFF				= 0,
	CHINOOK_FLYING						= 1,
	CHINOOK_DOING_COMBAT_DROP	= 2,
	CHINOOK_LANDING						= 3,
	CHINOOK_LANDED						= 4
};

//-------------------------------------------------------------------------------------------------
class ChinookAIUpdate : public SupplyTruckAIUpdate
{

	MEMORY_POOL_GLUE_WITH_USERLOOKUP_CREATE( ChinookAIUpdate, "ChinookAIUpdate" )
	MAKE_STANDARD_MODULE_MACRO_WITH_MODULE_DATA( ChinookAIUpdate, ChinookAIUpdateModuleData )

public:

	ChinookAIUpdate( Thing *thing, const ModuleData* moduleData );
	// virtual destructor prototype provided by memory pool declaration

	virtual UpdateSleepTime update();
 	virtual void aiDoCommand(const AICommandParms* parms);
	virtual Bool chooseLocomotorSet(LocomotorSetType wst);
	// this is present solely for some transports to override, so that they can land before 
	// allowing people to exit...
	virtual AIFreeToExitType getAiFreeToExit(const Object* exiter) const;
	virtual Bool isAllowedToAdjustDestination() const;
	virtual ObjectID getBuildingToNotPathAround() const;

	// this is present for subclasses (eg, Chinook) to override, to
	// prevent supply-ferry behavior in some cases (eg, when toting passengers)
	virtual Bool isAvailableForSupplying() const;
	virtual Bool isCurrentlyFerryingSupplies() const;
	
	virtual Bool isIdle() const;

	const ChinookAIUpdateModuleData* friend_getData() const { return getChinookAIUpdateModuleData(); }
	void friend_setFlightStatus(ChinookFlightStatus a) { m_flightStatus = a; }

protected:

	virtual AIStateMachine* makeStateMachine();

	virtual void privateCombatDrop( Object *target, const Coord3D& pos, CommandSourceType cmdSource );
	virtual void privateGetRepaired( Object *repairDepot, CommandSourceType cmdSource );///< get repaired at repair depot

private:

	void setMyState( StateID cmd, Object* target, const Coord3D* pos, CommandSourceType cmdSource );
	void setAirfieldForHealing(ObjectID id);

	AICommandParmsStorage		m_pendingCommand;
	ChinookFlightStatus			m_flightStatus;
	ObjectID								m_airfieldForHealing;
	Bool										m_hasPendingCommand;
};

#endif

