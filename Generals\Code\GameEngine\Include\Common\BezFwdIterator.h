/*
**	Command & Conquer Generals(tm)
**	Copyright 2025 Electronic Arts Inc.
**
**	This program is free software: you can redistribute it and/or modify
**	it under the terms of the GNU General Public License as published by
**	the Free Software Foundation, either version 3 of the License, or
**	(at your option) any later version.
**
**	This program is distributed in the hope that it will be useful,
**	but WITHOUT ANY WARRANTY; without even the implied warranty of
**	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
**	GNU General Public License for more details.
**
**	You should have received a copy of the GNU General Public License
**	along with this program.  If not, see <http://www.gnu.org/licenses/>.
*/

////////////////////////////////////////////////////////////////////////////////
//																																						//
//  (c) 2001-2003 Electronic Arts Inc.																				//
//																																						//
////////////////////////////////////////////////////////////////////////////////

#pragma once

#include "Common/BezierSegment.h"

class BezFwdIterator
{
	protected:
		Int mStep;
		Int mStepsDesired;

		BezierSegment mBezSeg;
		Coord3D mCurrPoint;
		
		Coord3D mDq;	// First Derivative
		Coord3D mDDq;	// Second Derivative
		Coord3D mDDDq;	// Third Derivative

	public:
		BezFwdIterator();
		BezFwdIterator(Int stepsDesired, const BezierSegment *bezSeg);
	
		void start(void);
		Bool done(void);
		const Coord3D& getCurrent(void) const;

		void next(void);
};